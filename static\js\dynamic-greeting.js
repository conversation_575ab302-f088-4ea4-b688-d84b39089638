/**
 * Dynamic Greeting Message
 *
 * This script adds a personalized greeting message above the chat input box
 * that changes based on the time of day and includes the user's name.
 */

document.addEventListener('DOMContentLoaded', () => {
    console.log('Dynamic greeting script loaded');

    // Initialize immediately (no delay)
    initDynamicGreeting();

    // Listen for login/logout events to update the greeting
    window.addEventListener('user-logged-in', () => {
        checkChatAndShowGreeting();
    });

    window.addEventListener('user-logged-out', () => {
        // Don't call hideGreetingMessage() on logout - that's for logged-in users
        // The pre-login UI will handle showing its own welcome container

        // Reset welcome title to default for non-logged-in users
        const welcomeTitle = document.getElementById('welcomeTitle');
        if (welcomeTitle) {
            welcomeTitle.innerHTML = '👋 Welcome to ZiaHR';
        }

        // Clear any existing greeting container for logged-in users
        const greetingContainer = document.getElementById('greetingMessageContainer');
        if (greetingContainer) {
            greetingContainer.remove();
        }
    });
    
    // Listen for new chat events to update the greeting
    window.addEventListener('new-chat-started', () => {
        const userData = JSON.parse(localStorage.getItem('user_data') || 'null');
        if (userData) {
            checkChatAndShowGreeting();
        }
    });
});

// We've removed the MutationObserver to improve performance

/**
 * Initialize the dynamic greeting
 */
function initDynamicGreeting() {
    try {
        // Check if user is logged in
        const userData = JSON.parse(localStorage.getItem('user_data') || 'null');

        if (userData) {
            // Check if chat is empty and show greeting if needed
            checkChatAndShowGreeting();
            
            // Get user's first name for the welcome title
            let userName = 'there';
            if (userData && userData.email) {
                // Try to extract a name from the email (before the @ symbol)
                const emailName = userData.email.split('@')[0];
                // Capitalize the first letter
                userName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
            }
            
            // Get time-appropriate greeting
            const greeting = getTimeBasedGreeting();
            
            // Update the welcome title
            updateWelcomeTitle(userName, greeting);
        } else {
            // Hide greeting if user is not logged in
            hideGreetingMessage();
        }
    } catch (error) {
        console.error('Error initializing dynamic greeting:', error);
    }
}

/**
 * Update the greeting message with user's name and time-appropriate greeting
 * @param {Object} userData - The user data object containing name information
 */
function updateGreetingMessage(userData) {
    try {
        // Get the greeting container
        let greetingContainer = document.getElementById('greetingMessageContainer');

        // If the greeting container doesn't exist, something is wrong as it should be in index.html
        if (!greetingContainer) {
            console.error('Greeting container not found. It should be present in index.html');
            return;
        }

        // Check if there are any user or bot messages in the chat
        const userMessages = document.querySelectorAll('.user-message, .bot-message');
        const hasMessages = userMessages.length > 0;

        // If there are messages, hide the greeting message container
        if (hasMessages) {
            if (greetingContainer) {
                greetingContainer.style.display = 'none';
            }
            return;
        }

        // Show the greeting container
        greetingContainer.style.display = 'block';

        // Fade in effect
        greetingContainer.style.opacity = '0';
        setTimeout(() => {
            greetingContainer.style.opacity = '1';
            addChipEventListeners(); // Add event listeners after the container is visible

            // Debug: Check if buttons are visible
            const buttons = greetingContainer.querySelectorAll('.suggestion-chip');
            console.log(`Found ${buttons.length} suggestion chips in greeting container`);
            buttons.forEach((btn, index) => {
                console.log(`Button ${index + 1}: "${btn.textContent.trim()}" - Display: ${getComputedStyle(btn).display}, Opacity: ${getComputedStyle(btn).opacity}`);
            });
        }, 10);
    } catch (error) {
        console.error('Error updating greeting message:', error);
    }
}

/**
 * Hide the greeting message
 */
function hideGreetingMessage() {
    try {
        // If we're in the process of starting a new chat, don't hide the greeting
        if (window.isStartingNewChat) {
            console.log('Not hiding greeting because new chat is starting');
            return;
        }

        const greetingContainer = document.getElementById('greetingMessageContainer');
        if (!greetingContainer) {
            return;
        }

        // Check if there are any messages in the chat
        // If there are no messages, don't hide the greeting
        const userMessages = document.querySelectorAll('.user-message, .bot-message');
        if (userMessages.length === 0) {
            console.log('Not hiding greeting because chat is empty');
            // Make sure it's visible
            if (greetingContainer) {
                greetingContainer.style.display = 'block';
                greetingContainer.style.opacity = '1';
            }
            return;
        }

        // Instantly hide the greeting (no fade-out, no delay)
        greetingContainer.style.opacity = '0';
        greetingContainer.style.display = 'none';
        console.log('Greeting message instantly hidden because chat has messages');
        if (typeof window.ensureChatInputCentered === 'function') {
            window.ensureChatInputCentered();
        }
    } catch (error) {
        console.error('Error hiding greeting message:', error);
    }
}

/**
 * Get a greeting based on the current time of day with appropriate emoji
 * @returns {string} A time-appropriate greeting with emoji
 */
function getTimeBasedGreeting() {
    try {
        const hour = new Date().getHours();

        if (hour >= 5 && hour < 12) {
            return '🌅 Good morning';
        } else if (hour >= 12 && hour < 18) {
            return '☀️ Good afternoon';
        } else if (hour >= 18 && hour < 22) {
            return '🌆 Good evening';
        } else {
            return '🌙 Good evening';
        }
    } catch (error) {
        console.error('Error getting time-based greeting:', error);
        return '👋 Hello';
    }
}

/**
 * Check if chat is empty and show greeting if needed
 */
function checkChatAndShowGreeting() {
    try {
        // Check if user is logged in
        const userData = JSON.parse(localStorage.getItem('user_data') || 'null');
        if (!userData) {
            console.log('User not logged in, not showing greeting');
            return;
        }

        // Check if there are any user or bot messages in the chat
        const userMessages = document.querySelectorAll('.user-message, .bot-message');
        const hasMessages = userMessages.length > 0;

        // Get or ensure the greeting container exists
        let greetingContainer = document.getElementById('greetingMessageContainer');

        // If no greeting container exists, we'll create one in updateGreetingMessage

        // Get user's first name for the welcome title
        let userName = 'there';
        if (userData && userData.email) {
            // Try to extract a name from the email (before the @ symbol)
            const emailName = userData.email.split('@')[0];
            // Capitalize the first letter
            userName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
        }
        
        // Get time-appropriate greeting
        const greeting = getTimeBasedGreeting();
        
        // Always update the welcome title, regardless of whether there are messages
        updateWelcomeTitle(userName, greeting);

        if (!hasMessages) {
            // Show the greeting message - this will create the container if needed
            updateGreetingMessage(userData);
            console.log('Chat is empty, showing greeting message');

            // Make sure the greeting is visible
            if (greetingContainer) {
                greetingContainer.style.display = 'block';
                greetingContainer.style.opacity = '1';
            }
        } else {
            // Hide the greeting message
            hideGreetingMessage();
            console.log('Chat has messages, hiding greeting message');
        }
    } catch (error) {
        console.error('Error checking chat and showing greeting:', error);
    }
}

/**
 * Update the welcome title with dynamic greeting
 * @param {string} userName - The user's name
 * @param {string} greeting - The time-based greeting
 */
function updateWelcomeTitle(userName, greeting) {
    try {
        // Find the dynamic welcome title element
        const welcomeTitleDynamic = document.getElementById('welcomeTitleDynamic');
        if (!welcomeTitleDynamic) {
            console.log('Dynamic welcome title not found');
            return;
        }

        // Update the dynamic welcome title with the personalized greeting
        welcomeTitleDynamic.innerHTML = `👋 Hi ${userName}, ${greeting}!`;
    } catch (error) {
        console.error('Error updating welcome title:', error);
    }
}

// Make functions available globally
window.updateGreetingMessage = updateGreetingMessage;
window.hideGreetingMessage = hideGreetingMessage;
window.checkChatAndShowGreeting = checkChatAndShowGreeting;
window.updateWelcomeTitle = updateWelcomeTitle;

/**
 * Add click event listeners to suggestion chips.
 */
function addChipEventListeners() {
    // Add click event to both .chip and .suggestion-chip for compatibility
    const chips = document.querySelectorAll('.chip, .suggestion-chip');
    chips.forEach(chip => {
        chip.addEventListener('click', (event) => {
            const chipText = event.currentTarget.textContent.trim();
            console.log('Chip clicked:', chipText);
            // Assuming a global function `sendChatMessage` exists to send messages
            if (typeof window.sendChatMessage === 'function') {
                window.sendChatMessage(chipText);
            } else {
                console.error('sendChatMessage function is not defined.');
                // Fallback for demonstration: log the message
                alert(`Simulating sending message: ${chipText}`);
            }
            hideGreetingMessage(); // Hide greeting after a chip is clicked
        });
    });
}
