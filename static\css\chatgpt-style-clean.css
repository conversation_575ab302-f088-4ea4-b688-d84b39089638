/* ChatGPT-style CSS - ZiaHR Color Palette */
:root {
    /* Light Theme (Default) - Black, White, Gray Theme */
    --bg-primary: #FFFFFF; /* Pure white for main chat panel */
    --bg-secondary: #F8F8F8; /* Light gray for secondary elements */
    --text-primary: #000000; /* Black for maximum readability */
    --text-secondary: #666666; /* Medium gray for de-emphasis */
    --accent-color: #333333; /* Dark gray accent color */
    --accent-color-rgb: 51, 51, 51; /* RGB values for accent color */
    --accent-hover: #555555; /* Medium gray on hover */
    --border-color: #DADADA; /* Light gray border color */
    --message-user-bg: #F2F2F2; /* Light gray for user messages */
    --message-bot-bg: #FFFFFF; /* White for bot messages */
    --message-text: #000000; /* Black for message text */
    --shadow-color: rgba(0, 0, 0, 0.06); /* Subtle shadow */
    --sidebar-bg: #F5F5F5; /* Light gray for sidebar */
    --sidebar-text: #000000; /* Black for sidebar text */
    --sidebar-hover: #EBEBEB; /* Slightly darker gray for hover */
    --sidebar-border: #EBEBEB; /* Light gray border color */
    --sidebar-item-hover: rgba(0, 0, 0, 0.03); /* Very subtle hover background */
    --sidebar-item-active: #E0E0E0; /* Light gray tint for active items */
    --sidebar-accent: #333333; /* Dark gray accent color for active items */
    --input-bg: #FFFFFF; /* Pure white for input background */
    --input-border: #DADADA; /* Light gray border for input */
    --input-text: #000000; /* Black for input text */
    --input-placeholder: #999999; /* Medium gray for placeholder */
    --welcome-bg: #FFFFFF; /* Match main background */
    --chip-bg: #F2F2F2; /* Light gray for suggestion chips */
    --chip-text: #000000; /* Black for chip text */
    --chip-hover-bg: #333333; /* Dark gray for chip hover */
    --chip-hover-text: #FFFFFF; /* White text on hover */
    --button-disabled: #E5E5E5; /* Light gray for disabled buttons */
    --cta-color: #333333; /* Dark gray for CTAs */
    --cta-hover: #555555; /* Medium gray for CTA hover */
    --success-color: #444444; /* Dark gray for success messages */
    --error-color: #666666; /* Medium gray for errors */
    --header-height: 60px;
    --footer-height: 100px;
    --sidebar-width: 260px; /* Increased from 220px (approximately 18% increase) */
    --sidebar-collapsed-width: 60px;
    --sidebar-min-width: 210px; /* Increased from 180px to maintain proportions */
    --sidebar-max-width: 300px; /* Kept the same max width */
    --resizer-width: 5px;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.06); /* Shadow for card-like elements */

    /* ChatGPT-specific variables */
    --chatgpt-message-spacing: 30px;
    --chatgpt-message-padding: 16px;
    --chatgpt-sidebar-item-height: 44px;
}

/* Dark Theme - Black, White, Gray Theme */
.theme-dark {
    --bg-primary: #1E1E1E; /* Dark background for main chat area */
    --bg-secondary: #2B2B2B; /* Slightly lighter for secondary elements */
    --text-primary: #FFFFFF; /* White text for dark mode */
    --text-secondary: #EDEDED; /* Light gray secondary text */
    --accent-color: #999999; /* Medium gray accent for dark mode */
    --accent-color-rgb: 153, 153, 153; /* RGB values for accent color */
    --accent-hover: #AAAAAA; /* Lighter gray on hover */
    --border-color: #333333; /* Dark gray border color */
    --message-user-bg: rgba(255, 255, 255, 0.1); /* Subtle white for user messages */
    --message-bot-bg: rgba(255, 255, 255, 0.05); /* Very subtle white for bot messages */
    --message-text: #FFFFFF; /* Match primary text */
    --shadow-color: rgba(0, 0, 0, 0.3); /* Deeper shadows */
    --sidebar-bg: #2B2B2B; /* Dark gray for sidebar */
    --sidebar-text: #FFFFFF; /* White text for sidebar */
    --sidebar-hover: #383838; /* Slightly lighter gray for hover */
    --sidebar-border: #333333; /* Dark gray border color */
    --sidebar-item-hover: rgba(255, 255, 255, 0.05); /* Subtle hover background */
    --sidebar-item-active: rgba(255, 255, 255, 0.1); /* Subtle white for active items */
    --sidebar-accent: #999999; /* Medium gray accent for active items */
    --input-bg: #343541; /* Dark gray for input background */
    --input-border: #444654; /* Slightly lighter border for input */
    --input-text: #FFFFFF; /* White text */
    --input-placeholder: #BBBBBB; /* Light gray placeholder */
    --welcome-bg: #1E1E1E; /* Match main background */
    --chip-bg: rgba(255, 255, 255, 0.1); /* Subtle white for chips */
    --chip-text: #FFFFFF; /* White text */
    --chip-hover-bg: #666666; /* Medium gray for hover */
    --chip-hover-text: #FFFFFF; /* Pure white on hover */
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Stronger shadow for dark mode */
    --cta-color: #999999; /* Medium gray for CTAs */
    --cta-hover: #AAAAAA; /* Lighter gray for hover */
    --success-color: #AAAAAA; /* Light gray for success */
    --error-color: #777777; /* Medium gray for errors */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

/* Remove any unwanted red outlines or borders */
.chat-messages *,
.welcome-container *,
.chat-input-container * {
    outline: none !important;
    border-color: var(--border-color) !important;
}

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.2s ease, color 0.2s ease;
    height: 100vh;
    overflow: hidden;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400; /* Regular weight for body text */
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography styles */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700; /* Bold weight for headings */
}

p, li, span, div {
    font-weight: 400; /* Regular weight for body text */
}



/* Hide welcome container for logged-in users */
html.logged-in .welcome-container,
body.logged-in .welcome-container {
    display: none !important;
}

/* Hide elements with not-logged-in-only class for logged-in users */
html.logged-in .not-logged-in-only,
body.logged-in .not-logged-in-only {
    display: none !important;
}

/* Show elements with not-logged-in-only class only for non-logged-in users */
html.not-logged-in .not-logged-in-only,
body.not-logged-in .not-logged-in-only {
    display: block;
}

.theme-dark body {
    background-color: #1E1E1E; /* Dark background for main chat area */
}

.theme-dark .chat-container {
    background-color: #1E1E1E; /* Dark background for main chat area */
}

.theme-dark .chat-container::after {
    background-color: #1E1E1E; /* Dark background for main chat area */
}

.app-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
    width: 100vw;
    max-width: 100vw;
    overflow-x: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: var(--bg-primary);
    box-sizing: border-box;
    width: 100%;
    max-width: 100vw;
}

/* Ensure chat input container stays centered in the app container */
.app-container .chat-input-container {
    left: 50%; /* Keep this for initial centering */
    transform: translateX(-50%); /* Keep this for centering based on its own width */
    margin-left: 0 !important;
    right: auto !important;
    /* Removed vertical centering from this rule */
}

/* Sidebar States */
.sidebar {
    min-width: 60px;
    max-width: 300px;
    width: var(--sidebar-width, 260px);
    transition: width 0.3s;
    overflow-x: hidden;
}

/* Collapsed sidebar state */
.sidebar.collapsed {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
}

/* Ensure chat input container stays centered regardless of sidebar state */
.sidebar.collapsed ~ .chat-container .chat-input-container {
    left: 50% !important;
    margin-left: 0 !important;
    right: auto !important;
    transform: translateX(-50%) !important; /* Center horizontally only */
}

.sidebar:not(.collapsed) ~ .chat-container .chat-input-container {
    /* Adjust left position to account for sidebar width */
    left: calc(50% + (var(--sidebar-width) / 2)) !important; /* Shift right by half sidebar width */
    margin-left: 0 !important;
    right: auto !important;
    transform: translateX(-50%) !important; /* Center horizontally based on its own width */
}

/* Apply different transform based on whether welcome message is present */
.sidebar.collapsed ~ .chat-container .welcome-container ~ .chat-input-container,
.sidebar:not(.collapsed) ~ .chat-container .welcome-container ~ .chat-input-container {
    transform: translate(-50%, -50%) !important; /* Center both horizontally and vertically */
}

/* After welcome message is removed, only center horizontally */
body.welcome-removed .chat-input-container {
    transform: translate(-50%, 0) !important; /* Center horizontally only */
    top: auto !important;
    bottom: 24px !important;
}

/* Hidden sidebar state (for backward compatibility) */
.sidebar.hidden {
    display: none;
}

.app-container.sidebar-hidden {
    width: 100%;
}

/* Sidebar top controls container */
.sidebar-top-controls {
    display: flex;
    align-items: center;
    padding: 10px;
    position: relative;
    z-index: 1000;
    width: 100%;
    justify-content: space-between;
    min-height: 50px;
    box-sizing: border-box;
    pointer-events: none; /* Make the container transparent to mouse events */
    border-bottom: 1px solid var(--sidebar-border);
}

/* But allow pointer events on all children */
.sidebar-top-controls > * {
    pointer-events: auto;
}

/* Sidebar title - empty by default */
.sidebar-title {
    color: var(--sidebar-text);
    font-size: 18px;
    font-weight: 600;
    margin-left: 8px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 40px;
    /* Empty by default - no text needed */
    height: 40px;
    display: flex;
    align-items: center;
}

/* Sidebar toggle button */
.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--sidebar-text);
    font-size: 18px;
    cursor: pointer;
    z-index: 1000; /* Increased z-index to ensure visibility */
    min-width: 32px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0; /* Prevent button from shrinking */
    box-shadow: none;
    outline: none;
    padding: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    overflow: hidden; /* Prevent content from overflowing */
    margin: 0 8px 0 0;
}

.sidebar-toggle:hover {
    background-color: var(--sidebar-item-hover);
    transform: scale(1.05);
}

.sidebar-toggle i {
    font-size: 18px;
    color: inherit;
    background: transparent;
    border: none;
    outline: none;
    display: block;
}

/* Ensure toggle button is visible when sidebar is collapsed */
.sidebar.collapsed .sidebar-toggle {
    position: fixed;
    left: 10px; /* Add a small margin from the left edge */
    top: 14px;
    background-color: transparent;
    border: none;
    z-index: 1020; /* Higher z-index to ensure it's above all other elements */
    box-shadow: none;
    padding: 0;
    outline: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure the icon inside the toggle button is properly styled when sidebar is collapsed */
.sidebar.collapsed .sidebar-toggle i {
    color: var(--text-primary);
    background: transparent;
    border: none;
    display: block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    position: relative;
    z-index: 1010; /* Higher z-index to ensure it's above other elements */
}

/* Hide elements when sidebar is collapsed */
.sidebar.collapsed .sidebar-brand-text,
.sidebar.collapsed .sidebar-item-text,
.sidebar.collapsed .sidebar-search,
.sidebar.collapsed .new-chat-btn span,
.sidebar.collapsed .sidebar-conversations,
.sidebar.collapsed .sidebar-bottom,
.sidebar.collapsed .sidebar-title {
    display: none;
}

/* Sidebar action buttons container */
.sidebar-action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    height: 40px;
    margin-right: 5px;
}

/* Hide action buttons when sidebar is collapsed */
.sidebar.collapsed .sidebar-action-buttons {
    display: none; /* Hide the buttons completely when sidebar is collapsed */
}

/* Show only icons when collapsed */
.sidebar.collapsed .sidebar-brand,
.sidebar.collapsed .sidebar-item {
    justify-content: center;
    padding: 12px 0;
}

.sidebar.collapsed .sidebar-item-icon {
    margin-right: 0;
}

.sidebar.collapsed .sidebar-top {
    display: flex;
    justify-content: center;
}

.sidebar.collapsed .new-chat-btn {
    width: 30px;
    height: 30px;
    padding: 0;
    justify-content: center;
    position: absolute;
    top: 60px; /* Position it below the hamburger icon */
    left: 10px;
    z-index: 1050; /* Higher z-index to ensure it's clickable */
    pointer-events: auto; /* Ensure it can receive click events */
    border-radius: 50%; /* Make it completely round */
    background-color: #000000; /* Black background in light mode */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.theme-dark .sidebar.collapsed .new-chat-btn {
    background-color: #FFFFFF; /* White background in dark mode */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Ensure correct icon colors for collapsed sidebar in both light and dark modes */
.sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none; /* Use the SVG's natural colors */
}

.theme-dark .sidebar.collapsed .new-chat-btn img.new-chat-icon {
    filter: none; /* Use the SVG's natural colors */
}

/* Chat container styles */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    /* Removed fixed height and overflow: hidden */
    position: relative;
    background-color: var(--bg-primary);
    width: 100%; /* Full width */
    margin: 0 auto; /* Center horizontally */
    align-items: center; /* Center content horizontally */
    justify-content: center; /* Center content vertically */
}

/* Removed the background that extends below the chat input */

.sidebar-nav {
    display: flex;
    flex-direction: column;
    height: calc(100% - 60px);
    width: 100%;
    overflow: hidden;
    padding: 8px;
    margin-top: 10px;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    margin-top: 30px; /* Add margin to avoid overlap with toggle button */
    gap: 12px;
    cursor: pointer;
    width: 100%;
}

.sidebar-brand-icon {
    font-size: 20px;
}

.sidebar-brand-text {
    font-size: 18px;
    font-weight: 600;
    text-align: center;
}

/* Sidebar header with title and action buttons side by side */
.sidebar-header {
    padding: 12px 16px;
    margin-bottom: 16px;
    margin-top: 40px; /* Add margin to avoid overlap with toggle button */
    width: 100%;
    text-align: center;
}

.sidebar-header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 30px;
}

/* Sidebar action buttons */
.sidebar-action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
    position: relative;
    z-index: 1050; /* Higher z-index to ensure buttons are clickable */
}

.sidebar-action-btn {
    background-color: transparent;
    color: var(--sidebar-text);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 18px;
    padding: 0;
    position: relative;
    z-index: 1050; /* Higher z-index to ensure buttons are clickable */
    margin: 0 8px;
    box-sizing: border-box;
    overflow: visible;
}

/* Specific styling for new chat button */
#newChatBtn {
    background-color: #000000 !important; /* Force black background in light mode */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-radius: 50%;
    overflow: visible; /* Changed from hidden to allow absolute positioning */
    width: 36px;
    height: 36px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative; /* Required for absolute positioned children */
}

/* Greeting message container styling - Clean and Compact */
.greeting-message-container {
    position: absolute;
    z-index: 5;
    max-width: 700px;
    width: 90%;
    margin: 0;
    padding: 20px;
    pointer-events: none;
    text-align: center;
    user-select: text;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 12px !important;
    justify-content: center;
    margin-top: 16px !important;
    max-width: 100%;
}

/* Clean greeting message title styling */
.greeting-message {
    color: var(--text-primary, #000000) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    margin: 0 0 16px 0 !important;
    text-align: center !important;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif !important;
}

.greeting-message-container .welcome-message h2 {
    margin-bottom: 16px !important;
    color: var(--text-primary, #000000) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
}

.greeting-message-container .welcome-message p {
    margin-bottom: 24px !important;
    color: var(--text-secondary, #666666) !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
}

/* Premium Suggestion Chip Styling - Matching Pre-login UI */
.suggestion-chip {
    background-color: var(--bg-primary, #FFFFFF);
    color: var(--text-primary, #000000);
    border: 1px solid var(--border-color, #E5E5E5);
    padding: 8px 16px !important;
    border-radius: 20px !important;
    cursor: pointer;
    font-size: 14px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    transform: translateY(0) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
    white-space: nowrap;
    pointer-events: auto;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    margin: 0 !important;
    font-weight: 400 !important;
    min-width: auto !important;
    max-width: none !important;
    width: auto !important;
    flex: none !important;
}

/* Shimmer effect for premium feel */
.suggestion-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

/* Premium hover effects matching pre-login UI */
.suggestion-chip:hover {
    background-color: rgba(0, 0, 0, 0.08) !important;
    border-color: var(--text-secondary, #666666) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.suggestion-chip:hover::before {
    left: 100%;
}

/* Active state for button press */
.suggestion-chip:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
    background-color: rgba(0, 0, 0, 0.12) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Responsive improvements for greeting message */
@media (max-width: 768px) {
    .greeting-message-container {
        max-width: 95%;
        padding: 16px;
    }

    .greeting-message-container .welcome-message {
        padding: 20px !important;
    }

    .suggestion-chips {
        gap: 8px !important;
    }

    .suggestion-chip {
        padding: 6px 12px !important;
        font-size: 13px !important;
        min-width: auto !important;
        width: auto !important;
    }
}

@media (max-width: 480px) {
    .greeting-message-container {
        max-width: 98%;
        padding: 12px;
    }

    .greeting-message-container .welcome-message {
        padding: 16px !important;
    }

    .greeting-message {
        font-size: 1.3rem !important;
    }

    .suggestion-chip {
        padding: 5px 10px !important;
        font-size: 12px !important;
        min-width: auto !important;
        width: auto !important;
    }
}

.greeting-message {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
}

/* Ensure the greeting message is visible when there are no messages */
#chatMessages:empty .greeting-message-container,
#chatMessages:not(:has(.user-message, .bot-message)) .greeting-message-container {
    display: block !important;
    opacity: 1 !important;
}

/* Dark mode styling for new chat button */
.theme-dark #newChatBtn {
    background-color: transparent !important; /* Transparent background to let the SVG show through */
    box-shadow: none; /* Remove shadow as the SVG has its own styling */
}

/* Light mode styling for new chat button */
#newChatBtn {
    background-color: transparent !important; /* Transparent background to let the SVG show through */
    box-shadow: none; /* Remove shadow as the SVG has its own styling */
}

/* No filters needed - SVG files already have correct colors */
#newChatBtn img.new-chat-icon {
    filter: none; /* Use the SVG's natural colors */
}

.theme-dark #newChatBtn img.new-chat-icon {
    filter: none; /* Use the SVG's natural colors */
}

/* New chat icon styling */
.new-chat-icon {
    width: 28px; /* Increased size for better visibility */
    height: 28px; /* Increased size for better visibility */
    display: block;
    position: absolute; /* Absolute positioning for perfect centering */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    filter: none !important; /* Ensure no filters are applied */
    /* Prevent any browser-specific image rendering issues */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Ensure no filters are applied to new chat icons in any context */
img.new-chat-icon {
    filter: none !important;
}

/* Force correct icon paths for dark mode */
.theme-dark #newChatBtn img.new-chat-icon,
.theme-dark .header-new-chat-btn img.new-chat-icon,
.theme-dark .search-modal .new-chat-icon img {
    content: url('/static/img/new-chat-icon-dark-larger.svg') !important;
}

/* Force correct icon paths for light mode */
.theme-light #newChatBtn img.new-chat-icon,
.theme-light .header-new-chat-btn img.new-chat-icon,
.theme-light .search-modal .new-chat-icon img,
body:not(.theme-dark) #newChatBtn img.new-chat-icon,
body:not(.theme-dark) .header-new-chat-btn img.new-chat-icon,
body:not(.theme-dark) .search-modal .new-chat-icon img {
    content: url('/static/img/new-chat-icon-larger.svg') !important;
}

.sidebar-action-btn:hover {
    background-color: var(--sidebar-item-hover);
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* New chat button hover */
#newChatBtn:hover {
    transform: scale(1.05);
}

/* Dark mode hover effect */
.theme-dark .sidebar-action-btn:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* Dark mode hover effect for new chat button */
.theme-dark #newChatBtn:hover {
    transform: scale(1.05);
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1001;
    pointer-events: none;
    white-space: nowrap;
    display: none;
}

.sidebar-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: transparent;
    border: none;
    color: var(--sidebar-text);
    font-size: 18px;
    cursor: pointer;
    display: none;
}

.sidebar-top {
    padding: 0 8px;
    margin-bottom: 8px;
}

.new-chat-btn {
    background-color: #000000;
    color: #FFFFFF;
    border: none;
    padding: 12px 16px;
    border-radius: 50px; /* Make it completely round */
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.theme-dark .new-chat-btn {
    background-color: #FFFFFF;
    color: #000000;
}

.new-chat-btn:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.theme-dark .new-chat-btn:hover {
    box-shadow: 0 2px 5px rgba(255, 255, 255, 0.2);
}

/* Sidebar Search */
.sidebar-search {
    padding: 8px;
    margin-bottom: 8px;
}

.search-input-wrapper {
    display: flex;
    position: relative;
    width: 100%;
}

.search-input {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--sidebar-text);
    border: 1px solid var(--sidebar-border);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    width: 100%;
    transition: all 0.2s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--accent-color);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--sidebar-text);
    cursor: pointer;
    font-size: 14px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.search-btn:hover {
    opacity: 1;
}

/* Sidebar chat list: subtle scrollbar */
.sidebar-conversations {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}
.sidebar-conversations::-webkit-scrollbar {
  width: 4px;
  background: transparent;
}
.sidebar-conversations::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}
.sidebar-conversations::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Sticky header for chat-history-header */
.chat-history-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--sidebar-bg);
  padding: 10px 16px;
  margin-top: 0;
  margin-bottom: 4px;
  border-bottom: 1px solid var(--sidebar-border);
}

/* Prevent chat items from overlapping header */
.chat-history-list {
  padding-top: 0;
}

/* Ellipsis for chat names */
.chat-history-item-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

/* Extra: ensure chat items have margin below header */
.chat-history-header + .chat-history-item {
  margin-top: 2px;
}

.chat-history-item {
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
    color: var(--sidebar-text);
    position: relative;
    margin-bottom: 1px;
    line-height: 1.4;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.chat-history-item:hover {
    background-color: var(--sidebar-item-hover);
}

/* Dark theme version of hover state */
.theme-dark .chat-history-item:hover {
    background-color: var(--sidebar-item-hover);
}

.chat-history-item.active {
    background-color: var(--sidebar-item-active);
    border-left: 3px solid var(--sidebar-accent);
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    border-radius: 4px;
}

/* Dark theme version of active chat item */
.theme-dark .chat-history-item.active {
    background-color: var(--sidebar-item-active);
    border-left: 3px solid var(--sidebar-accent);
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.chat-history-item-content {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 8px;
}

.chat-history-item-title {
    font-weight: 500;
    position: relative;
    color: var(--sidebar-text);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 230px; /* Increased from 200px to accommodate wider sidebar */
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.chat-history-item.active .chat-history-item-title {
    font-weight: 700;
}

/* Styles for inline editing of chat titles */
.chat-history-item-title-input {
    background: transparent;
    border: none;
    outline: none;
    color: var(--sidebar-text);
    font-size: 0.875rem;
    font-weight: 500;
    width: 100%;
    padding: 0;
    margin: 0;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.chat-history-item-title-input:focus {
    border-bottom: 1px solid var(--sidebar-text);
}

.theme-dark .chat-history-item-title-input {
    color: var(--sidebar-text);
    background-color: rgba(10, 16, 33, 0.5);
}

.chat-history-item-menu {
    color: var(--sidebar-text);
    opacity: 0.5;
    font-size: 14px;
    padding: 4px;
    cursor: pointer;
    position: relative;
}

.chat-history-item-menu:hover {
    opacity: 0.8;
}

.chat-menu-dropdown {
    position: absolute; /* Absolute positioning relative to the document */
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1001; /* Higher z-index to ensure it appears above other elements */
    min-width: 180px;
    display: block;
    padding: 8px 0;
    opacity: 0;
    transform: translateY(-5px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    pointer-events: none;
}

.chat-menu-dropdown.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.chat-menu-item {
    padding: 10px 16px;
    cursor: pointer;
    color: #333;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-menu-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Dark mode styles for dropdown menu */
.theme-dark .chat-menu-dropdown {
    background-color: #1E1E1E;
    border-color: #444654;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.theme-dark .chat-menu-item {
    color: #FFFFFF;
}

.theme-dark .chat-menu-item:hover {
    background-color: #343541;
}

.theme-dark .chat-menu-item.delete {
    color: #ff6b6b;
}

.chat-menu-item i {
    width: 16px;
    text-align: center;
}

.chat-menu-item.rename i,
.chat-menu-item.archive i,
.chat-menu-item.share i {
    color: #333;
}

.chat-menu-item.download i {
    color: #333;
}

.chat-menu-item.delete {
    color: var(--error-color);
}

.chat-menu-item.delete i {
    color: var(--error-color);
}

.chat-menu-item.delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Dark mode styles for chat menu dropdown */
.theme-dark .chat-menu-dropdown {
    background-color: #1E1E1E;
    border: 1px solid #444654;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.theme-dark .chat-menu-item {
    color: #FFFFFF;
}

.theme-dark .chat-menu-item:hover {
    background-color: #343541;
}

.theme-dark .chat-menu-item.rename i,
.theme-dark .chat-menu-item.archive i,
.theme-dark .chat-menu-item.share i,
.theme-dark .chat-menu-item.download i {
    color: #EDEDED;
}

.theme-dark .chat-menu-item.delete {
    color: var(--cta-color);
}

.theme-dark .chat-menu-item.delete i {
    color: var(--cta-color);
}

.theme-dark .chat-menu-item.delete:hover {
    background-color: rgba(255, 107, 107, 0.15);
}

/* Notification dialog */
.notification-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 20px;
    z-index: 1000;
    max-width: 400px;
    width: 100%;
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

/* Search results styles */
.search-results {
    margin-top: 15px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.search-results::-webkit-scrollbar {
    display: none;
}

.search-prompt {
    color: var(--text-secondary);
    text-align: center;
    padding: 20px 0;
    font-size: 0.875rem;
    font-weight: 400;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: flex-start;
}

.search-result-item:before {
    content: "\f15c"; /* Document icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 400;
    margin-right: 12px;
    color: var(--text-secondary);
    font-size: 14px;
}

.search-result-content {
    flex: 1;
}

.search-result-item:hover {
    background-color: var(--bg-secondary);
}

.search-result-title {
    font-weight: 500;
    margin-bottom: 4px;
    font-size: 14px;
}

.search-result-snippet {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.search-result-snippet .highlight {
    color: var(--accent-color);
    font-weight: 600;
    background-color: rgba(var(--accent-color-rgb), 0.1);
    padding: 1px 3px;
    border-radius: 3px;
}

/* Search form styles */
.search-form .form-group {
    position: relative;
}

.search-form .form-control {
    width: 100%;
    padding: 10px 16px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-form .form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

/* New chat option in search */
.new-chat-option {
    margin-top: 15px;
}

.new-chat-item {
    border-bottom: none !important;
}

.new-chat-item:before {
    content: "\f067" !important; /* Plus icon */
    font-weight: 900 !important;
}

/* Search section divider */
.search-section-divider {
    font-size: 12px;
    color: var(--text-secondary);
    padding: 8px 16px;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    margin-top: 5px;
}

.search-result-snippet {
    font-size: 14px;
    color: var(--text-secondary);
}

.search-result-snippet .highlight {
    background-color: rgba(108, 74, 182, 0.2);
    color: var(--accent-color);
    padding: 0 2px;
    border-radius: 2px;
    font-weight: 500;
}

.theme-dark .search-result-snippet .highlight {
    background-color: rgba(138, 112, 214, 0.2);
    color: var(--accent-color);
}

/* Edit options styles */
.edit-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
}

.edit-option-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
    font-size: 15px;
    text-align: left;
}

.edit-option-btn:hover {
    background-color: var(--bg-primary);
    border-color: var(--accent-color);
}

.edit-option-btn i {
    font-size: 18px;
    color: var(--accent-color);
    width: 24px;
    text-align: center;
}

/* Settings Modal Styles */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.settings-tab {
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.settings-tab:hover {
    color: var(--text-primary);
}

.settings-tab.active {
    color: var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
}

.settings-panel {
    display: none;
}

.settings-panel.active {
    display: block;
}

.settings-section {
    margin-bottom: 24px;
}

.settings-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.settings-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.settings-option label {
    font-size: 14px;
    color: var(--text-primary);
}

/* Archived Chats Styles */
.archived-chats-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--bg-secondary);
}

.archived-chat-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.archived-chat-item:last-child {
    border-bottom: none;
}

.archived-chat-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.archived-chat-date {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.archived-chat-actions {
    display: flex;
    gap: 8px;
}

.archived-chat-action {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.archived-chat-action:hover {
    background-color: rgba(108, 74, 182, 0.1);
}

.archived-chat-action.delete {
    color: var(--error-color);
}

.archived-chat-action.delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.no-archived-chats {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
}

/* Archived Chats Modal Styles */
#archivedChatsModal .modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
}

#archivedChatsModal .modal-body {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
}

.archived-chats-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.loading-spinner {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

.no-archived-chats-message {
    text-align: center;
    padding: 30px 20px;
    color: var(--text-secondary);
}

.no-archived-chats-message p {
    margin: 10px 0;
}

/* Dark mode specific styles for archived chats modal */
.theme-dark #archivedChatsModal .modal-content {
    background-color: #2B2B2B;
    color: #E0E0E0;
    border: 1px solid #3A3A3A;
}

.theme-dark #archivedChatsModal .modal-header {
    border-bottom: 1px solid #3A3A3A;
}

.theme-dark #archivedChatsModal .modal-footer {
    border-top: 1px solid #3A3A3A;
}

.theme-dark .archived-chat-item {
    border-bottom: 1px solid #3A3A3A;
}

.theme-dark .archived-chat-item:hover {
    background-color: #3A3A3A;
}

.theme-dark .archived-chat-title {
    color: #E0E0E0;
}

.theme-dark .archived-chat-date {
    color: #AAAAAA;
}

.theme-dark .no-archived-chats-message,
.theme-dark .loading-spinner {
    color: #AAAAAA;
}

/* Login and Register Form Styles */
.login-form {
    margin-bottom: 10px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.8125rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.form-control {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-size: 0.8125rem;
    transition: all 0.2s ease;
    background-color: var(--input-bg);
    color: var(--input-text);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

.form-control:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 3px rgba(108, 74, 182, 0.3);
}

.login-message {
    color: var(--error-color); /* Crimson Red for errors */
    font-size: 0.875rem;
    margin-top: 10px;
    min-height: 20px;
    font-weight: 500;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.theme-dark .login-message {
    color: #ff6b6b; /* Brighter red for dark mode */
}

.modern-login {
    max-width: 340px;
    border-radius: 6px;
    overflow: hidden;
    background-color: var(--bg-primary);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
    border: 1px solid var(--border-color);
}

.modern-login .modal-header {
    padding: 10px 16px;
    position: relative;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.modern-login .modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.modern-login .modal-body {
    padding: 12px 16px;
}

.modern-login .form-group {
    margin-bottom: 12px;
}

.forgot-password-container {
    margin-bottom: 12px;
}

.forgot-password {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 0.75rem;
    display: block;
    font-weight: 400;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.forgot-password:hover {
    text-decoration: underline;
    color: var(--accent-hover);
}

.btn-block {
    width: 100%;
    padding: 6px 8px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    background-color: var(--accent-color);
    color: white;
    border: 1px solid var(--accent-hover);
    cursor: pointer;
    text-transform: none;
    letter-spacing: 0.3px;
    margin-bottom: 10px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.btn-block:hover {
    background-color: var(--accent-hover);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.register-link-container {
    text-align: center;
    font-size: 11px;
    color: var(--text-secondary);
}

.register-link-container a {
    color: var(--accent-color);
    text-decoration: none;
    margin-left: 4px;
    font-weight: 500;
}

.register-link-container a:hover {
    text-decoration: underline;
    color: var(--accent-hover);
}

.login-actions-container {
    margin-top: 16px;
    text-align: center;
}

.btn-text {
    background: transparent;
    border: none;
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 16px;
    transition: all 0.2s;
}

.btn-text:hover {
    text-decoration: underline;
}

/* Password input wrapper */
.password-input-wrapper {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.password-toggle-btn:hover {
    color: var(--accent-color);
}

/* Analytics Dashboard Styles removed */

.notification-dialog-header {
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 1rem;
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.notification-dialog-content {
    margin-bottom: 20px;
    font-size: 0.875rem;
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

.notification-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.notification-dialog-button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
}

.notification-dialog-button:hover {
    background-color: var(--accent-hover);
}

.notification-dialog-button.cancel-button {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.notification-dialog-button.cancel-button:hover {
    background-color: #e0e0e0;
}

.notification-dialog-button.confirm-button {
    font-weight: 500;
}

/* Simple Toast Notification */
.simple-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background-color: #4CAF50; /* Green background for success/info */
    color: white;
    padding: 16px 24px;
    border-radius: 4px;
    font-size: 1rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
    z-index: 1001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 80%;
}

/* Error Toast Notification */
.simple-toast.error {
    background-color: #DC3545; /* Red background for errors */
}

/* Success Toast Notification */
.simple-toast.success {
    background-color: #4CAF50; /* Green background for success */
}

.simple-toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.toast-close-btn {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    margin-left: 16px;
    padding: 0 4px;
    line-height: 1;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.notification-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Share Dialog Styles */
.share-dialog {
    max-width: 450px;
}

.share-link-container {
    display: flex;
    margin: 15px 0;
    width: 100%;
}

.share-link-input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    font-size: 0.875rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

.copy-link-btn {
    padding: 10px 15px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
}

.copy-link-btn:hover {
    background-color: var(--accent-hover);
}

.share-options {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.share-option {
    flex: 1;
    min-width: 80px;
    padding: 10px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.share-option i {
    font-size: 18px;
    margin-bottom: 5px;
}

.share-option:hover {
    background-color: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.theme-dark .share-link-input {
    background-color: rgba(22, 32, 66, 0.8);
    border-color: rgba(79, 139, 249, 0.3);
    color: #f8fafc;
}

.theme-dark .share-option {
    background-color: rgba(22, 32, 66, 0.8);
    border-color: rgba(79, 139, 249, 0.3);
}

.theme-dark .share-option:hover {
    background-color: #4f8bf9;
    border-color: #4f8bf9;
}

/* Archived chat styles */
.chat-history-item.archived .chat-history-item-title {
    opacity: 0.7;
}

.archived-indicator {
    font-size: 12px;
    color: var(--text-secondary);
    margin-left: 5px;
}

.theme-dark .archived-indicator {
    color: rgba(209, 216, 230, 0.7);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999; /* Very high z-index to ensure it's above everything */
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
    color: var(--text-primary);
}

/* Search Modal Specific Styles */
.search-modal-content {
    max-width: 600px;
    width: 90%;
    border: 1px solid var(--border-color);
}

.modal-lg {
    max-width: 700px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px; /* Reduced from 15px 20px */
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: 16px; /* Reduced from 18px */
    font-weight: 600;
    color: var(--text-primary);
}

.modal-body {
    padding: 16px; /* Reduced from 20px */
    max-height: 60vh; /* Reduced from 70vh */
    overflow-y: auto;
}

/* Password input styles */
.password-input-wrapper .password-toggle-btn,
.password-input-wrapper .password-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-input-wrapper input[type="password"],
.password-input-wrapper input[type="text"] {
    padding-right: 40px;
}

/* Modal footer with left and right sections */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px; /* Reduced from 15px 20px */
    border-top: 1px solid var(--border-color);
}

.modal-footer-left {
    text-align: left;
}

.modal-footer-right {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 14px;
}

.modal-footer a:hover {
    text-decoration: underline;
}

/* Original modal footer style overridden by the new one above */

.btn {
    padding: 6px 12px; /* Reduced from 8px 16px */
    border-radius: 4px;
    font-size: 13px; /* Reduced from 14px */
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    border: none;
}

.btn-primary {
    background-color: #f5f5f5;
    color: #000000;
    border: 1px solid #d0d0d0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background-color: #e8e8e8;
    border-color: #c0c0c0;
}

.btn-primary:active {
    background-color: #e0e0e0;
    border-color: #b0b0b0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #000000;
    border: 1px solid #d0d0d0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background-color: #e8e8e8;
    border-color: #c0c0c0;
}

.btn-secondary:active {
    background-color: #e0e0e0;
    border-color: #b0b0b0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.icon-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s, color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.icon-button:hover {
    background-color: var(--bg-hover);
    color: var(--accent-color);
}

.chat-history-action-btn {
    background: transparent;
    border: none;
    color: var(--sidebar-text);
    font-size: 12px;
    cursor: pointer;
    padding: 4px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.chat-history-action-btn:hover {
    opacity: 1;
}

.chat-history-item:hover {
    background-color: var(--sidebar-item-hover);
}

.chat-history-item.active {
    background-color: var(--sidebar-item-active);
}

.empty-history, .no-search-results {
    padding: 12px;
    color: var(--sidebar-text);
    font-size: 0.875rem;
    text-align: center;
    opacity: 0.7;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
}

.sidebar-bottom {
    padding: 8px;
    border-top: 1px solid var(--sidebar-border);
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;
    height: var(--chatgpt-sidebar-item-height);
    position: relative;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.sidebar-item:hover {
    background-color: var(--sidebar-item-hover);
}

.sidebar-item.active {
    background-color: var(--sidebar-item-active);
    border-left: 3px solid var(--sidebar-accent);
    font-weight: 700;
}

.sidebar-item-icon {
    width: 20px;
    text-align: center;
    margin-right: 12px;
}

.sidebar-item-text {
    flex: 1;
    font-size: 0.875rem;
}

.sidebar-item-toggle {
    margin-left: 12px;
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--accent-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.slider.round {
    border-radius: 20px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Upload status */
.upload-status {
    font-size: 12px;
    padding: 0 12px;
    color: var(--accent-color);
    margin-bottom: 8px;
}

/* Chat Container Styles - ChatGPT-specific */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    /* Removed fixed height and overflow: hidden */
    position: relative;
    background-color: var(--bg-primary);
    width: 100%; /* Full width */
    margin: 0 auto; /* Center horizontally */
    align-items: center; /* Center content horizontally */
    justify-content: center; /* Center content vertically */
}

.chat-header {
    height: var(--header-height);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    padding: 0 16px;
    border-bottom: 1px solid var(--border-color);
    z-index: 10;
    width: 100%; /* Full width header */
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: 0 20px;
    position: relative;
}

/* Ensure proper spacing in the header when sidebar is collapsed */
.sidebar.collapsed ~ .chat-container .header-content {
    padding-left: 0; /* Remove padding to eliminate gap */
    padding-right: 20px; /* Keep right padding consistent */
}

.sidebar-toggle-header {
    background: transparent;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    z-index: 1000;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 0;
    margin-right: 8px;
}

.sidebar-toggle-header:hover {
    background-color: var(--bg-secondary);
    color: var(--accent-color);
}

.search-button {
    margin-right: 16px;
}

.sidebar-toggle:hover {
    background-color: transparent;
    color: var(--accent-color);
}

.header-left {
    grid-column: 1;
}

.chat-title {
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    flex: 1;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: flex-end; /* Align to the right */
    margin-right: 0;
    padding-right: 0;
    position: relative; /* Changed from absolute to relative */
}

.header-action-btn {
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.header-action-btn:hover {
    background-color: var(--bg-secondary);
}

.header-theme-toggle {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

/* User account dropdown styles */
.user-account-dropdown {
    position: fixed;
    display: inline-block;
    right: 20px;
    top: 14px;
    z-index: 1050;
}

.login-icon {
    cursor: pointer;
    padding: 8px;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    width: 36px;
    height: 36px;
    border-radius: 50%;
}

.login-icon i {
    font-size: 20px;
    color: #374151;
}

.login-icon:hover {
    background-color: var(--bg-secondary);
}

/* User dropdown styles */
.user-dropdown {
    position: absolute;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 240px;
    z-index: 1000;
    overflow: hidden;
}

.user-dropdown-header {
    padding: 14px 16px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.user-email {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

.user-dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.user-dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.user-dropdown-item:hover {
    background-color: var(--bg-secondary);
}

.theme-dark .user-dropdown-item.logout {
    color: #ff6b6b; /* Brighter red for dark mode */
}

.user-dropdown-item.logout {
    color: #ef4444; /* Red for light mode */
}

/* Chat Messages Area - ChatGPT-specific */
.chat-messages {
    flex: 1 1 auto;
    width: 100%;
    max-width: 100vw;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 32px 24px 0 24px;
    box-sizing: border-box;
}
@media (max-width: 768px) {
  .chat-messages {
    padding: 16px 4vw 0 4vw;
  }
}
@media (max-width: 576px) {
  .chat-messages {
    padding: 8px 2vw 0 2vw;
  }
}

.welcome-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    flex-grow: 1; /* Allows it to take up available space */
    padding: 20px;
    max-width: 800px; /* Adjusted to widen the welcome container */
    margin: 0 auto;
    position: relative;
    z-index: 10;
    box-sizing: border-box; /* Ensures padding is included in the width */
}

/* Clean Welcome Message Box - Matching Pre-login Style */
.greeting-message-container .welcome-message,
.logged-in .greeting-message-container .welcome-message,
#greetingMessageContainer .welcome-message {
    background-color: var(--bg-primary, #FFFFFF) !important;
    border-radius: 12px !important;
    padding: 28px !important;
    max-width: 700px !important;
    width: 90% !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
    text-align: center !important;
    margin: 0 auto !important;
    border: 1px solid var(--border-color, #E5E5E5) !important;
    box-sizing: border-box !important;
    position: relative !important;
    transition: transform 0.3s ease-out !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    pointer-events: auto !important; /* Enable interactions */
}

/* Subtle animated background pattern */
.greeting-message-container .welcome-message::before,
#greetingMessageContainer .welcome-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    animation: subtleFloat 8s ease-in-out infinite;
    z-index: 0;
    border-radius: 24px;
}

.greeting-message-container .welcome-message > *,
#greetingMessageContainer .welcome-message > * {
    position: relative;
    z-index: 1;
}

/* Premium Dark Theme Welcome Message */
.theme-dark .greeting-message-container .welcome-message,
.theme-dark #greetingMessageContainer .welcome-message {
    background:
        linear-gradient(145deg, rgba(30, 30, 30, 0.95) 0%, rgba(20, 20, 20, 0.9) 100%),
        radial-gradient(circle at 20% 20%, rgba(147, 197, 253, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(196, 181, 253, 0.03) 0%, transparent 50%) !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 8px 16px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
}

.theme-dark .greeting-message-container .welcome-message::before,
.theme-dark #greetingMessageContainer .welcome-message::before {
    background:
        radial-gradient(circle at 30% 30%, rgba(147, 197, 253, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(196, 181, 253, 0.04) 0%, transparent 50%);
}

/* Subtle hover effect for the entire welcome box */
.greeting-message-container .welcome-message:hover,
#greetingMessageContainer .welcome-message:hover {
    transform: translateY(-2px) !important;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.12),
        0 12px 20px rgba(0, 0, 0, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

.theme-dark .greeting-message-container .welcome-message:hover,
.theme-dark #greetingMessageContainer .welcome-message:hover {
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 12px 20px rgba(0, 0, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.greeting-message-container .welcome-message h2,
#greetingMessageContainer .welcome-message h2 {
    margin-bottom: 12px !important; /* Reduced margin */
    color: var(--text-primary) !important;
    font-size: 1.5rem !important;
    font-weight: 700 !important;
}

.greeting-message-container .welcome-message p,
#greetingMessageContainer .welcome-message p {
    margin-bottom: 20px !important; /* Reduced margin */
    color: var(--text-secondary) !important;
    font-weight: 400 !important;
    font-size: 1rem !important;
}

/* Ensure suggestion chips container is visible and properly styled */
.greeting-message-container .suggestion-chips,
#greetingMessageContainer .suggestion-chips,
.welcome-message .suggestion-chips {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
    justify-content: center !important;
    margin-top: 16px !important;
    max-width: 100% !important;
    width: 100% !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
    z-index: 10 !important;
    position: relative !important;
}















/* Responsive improvements for mobile */
@media (max-width: 768px) {
    .welcome-message {
        padding: 28px;
        width: 95%;
    }

    .suggestion-chips {
        gap: 10px;
    }

    .suggestion-chip {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .welcome-message {
        padding: 16px;
        width: 95%;
    }

    .greeting-message {
        font-size: 1.5rem;
        padding: 16px 0;
    }

    .welcome-message p {
        font-size: 0.9rem;
        margin-bottom: 20px;
    }

    .suggestion-chips {
        flex-direction: column;
        gap: 8px;
    }


}

/* Message Styles - ChatGPT-specific */
.message {
    padding: 8px var(--chatgpt-message-spacing);
    margin-bottom: 8px;
    animation: fadeIn 0.3s ease;
    display: flex;
    flex-direction: column;
    background-color: transparent; /* Remove background */
    border-bottom: none; /* Remove border */
    width: 100%; /* Full width */
    max-width: 768px; /* Match container width */
    margin-left: auto;
    margin-right: auto;
}

.user-message {
    align-items: flex-end; /* Align user messages to the right */
}

.bot-message {
    align-items: flex-start; /* Align bot messages to the left */
    margin-left: 0;
    padding-left: 0;
    padding-right: var(--chatgpt-message-spacing);
}

.system-message {
    align-items: center; /* Center system messages */
    opacity: 0.8;
}

.message-content {
    color: var(--message-text);
    font-size: 1rem;
    line-height: 1.6;
    font-weight: 400;
    max-width: 85%; /* Slightly increased width of message content */
    padding: 14px 18px; /* Increased padding for better spacing */
    border-radius: 12px; /* Consistent rounded corners */
}

.bot-message .message-content {
    max-width: 95%; /* Wider content for bot messages */
    background-color: var(--message-bot-bg);
    box-shadow: var(--card-shadow); /* Subtle shadow for card-like appearance */
    border: 1px solid rgba(0, 0, 0, 0.03); /* Very subtle border */
}

.user-message .message-content {
    background-color: var(--message-user-bg); /* Light blue background for user messages */
    color: var(--text-primary);
    border-radius: 12px 12px 0 12px; /* Rounded corners with one sharp corner */
    box-shadow: var(--card-shadow); /* Subtle shadow for card-like appearance */
}

.bot-message .message-content {
    margin-left: 0;
    color: var(--text-primary); /* Ensure text is visible in light mode */
}

.theme-dark .bot-message .message-content {
    background-color: rgba(255, 255, 255, 0.05); /* Very subtle background in dark mode */
    border: 1px solid rgba(255, 255, 255, 0.03); /* Very subtle border */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow for dark mode */
    color: var(--text-primary); /* Ensure text is visible in dark mode */
}

/* Dark theme user messages */
.theme-dark .user-message .message-content {
    background-color: rgba(77, 168, 218, 0.15); /* Subtle blue for dark theme */
    color: var(--text-primary); /* Match text color */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow for dark mode */
}

.message-content p {
    margin-bottom: 16px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul, .message-content ol {
    margin-bottom: 16px;
    padding-left: 24px;
}

.message-content pre {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin-bottom: 16px;
}

.theme-dark .message-content pre {
    background-color: rgba(10, 16, 33, 0.8);
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25), inset 0 1px 2px rgba(79, 139, 249, 0.1);
}

.message-content code {
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
}

/* Escalation confirmation styles */
.escalation-confirmation {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
}

.escalation-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.confirm-escalation {
    background-color: var(--accent-color);
    color: white;
    border: none;
}

.confirm-escalation:hover {
    background-color: var(--accent-hover);
}

.cancel-escalation {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.cancel-escalation:hover {
    background-color: var(--bg-secondary);
}

.message-footer {
    max-width: 85%; /* Match the message content width */
    margin: 4px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.bot-message .message-footer {
    max-width: 95%; /* Match the wider bot message content */
}

.user-message .message-footer {
    justify-content: flex-end;
}

.bot-message .message-footer {
    justify-content: space-between;
    align-items: center;
    padding-left: 0;
    margin-left: 0;
}

.message-time {
    font-size: 12px;
    color: var(--text-secondary);
    margin-left: 8px;
    white-space: nowrap;
    font-weight: 400;
    opacity: 0.8;
}

/* Message feedback buttons */
.message-feedback {
    display: flex;
    gap: 8px;
    margin-right: 12px;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
}

.feedback-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    position: relative;
}

.feedback-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--accent-color);
}

.feedback-btn.active {
    color: var(--accent-color);
    background-color: rgba(var(--accent-color-rgb), 0.1);
}

.time-container {
    display: flex;
    align-items: center;
    margin-left: auto;
    flex-shrink: 0;
}



/* Chat Input Area - ChatGPT-specific */
.chat-input-container {
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    height: 48px !important;
    min-height: 48px !important;
    max-width: 600px !important;
    width: 80vw !important;
    padding: 0 4px !important;
    border-radius: 8px !important;
    margin: 16px auto 0 auto !important;
    background: #fff !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.06) !important;
    border: 1px solid #bdbdbd !important;
}
@media (max-width: 768px) {
  .chat-input-container {
    padding: 0 2vw 12px 2vw;
  }
}
@media (max-width: 576px) {
  .chat-input-container {
    padding: 0 1vw 8px 1vw;
  }
}

.chat-input-form {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    height: 100% !important;
}

.input-wrapper {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1 1 auto !important;
}

textarea, #userInput {
    min-height: 28px !important;
    max-height: 32px !important;
    height: 28px !important;
    padding: 6px 12px 0 0 !important;
    font-size: 1rem !important;
    line-height: 1.4 !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    color: #222 !important;
    resize: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    width: 100% !important;
    flex: 1 1 auto !important;
    display: block !important;
}

.input-actions-row {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-end !important;
    justify-content: space-between !important;
    padding: 0 !important;
    margin: 0 !important;
    gap: 0 !important;
}

.input-actions-left {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-end !important;
    gap: 8px !important;
}

.input-action-btn, .action-btn {
    width: 22px !important;
    height: 22px !important;
    min-width: 22px !important;
    min-height: 22px !important;
    padding: 0 !important;
    margin: 0 4px 0 0 !important;
    font-size: 18px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 4px !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    transition: background 0.2s;
}

.send-btn {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
    min-height: 32px !important;
    margin: 0 0 0 8px !important;
    background: #333 !important;
    color: #fff !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 8px !important;
}

.input-actions-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.left-actions, .right-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* New horizontal layout for message input */
.input-horizontal-row {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  width: 100% !important;
  padding: 8px 12px !important;
}

.input-horizontal-row .left-actions {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  flex-shrink: 0 !important;
}

.input-horizontal-row .input-main-area {
  flex: 1 !important;
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
}

.input-horizontal-row .input-main-area textarea {
  width: 100% !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  resize: none !important;
  font-size: 1rem !important;
  padding: 8px 12px !important;
  min-height: 20px !important;
  max-height: 80px !important;
  line-height: 1.4 !important;
}

.input-horizontal-row .right-actions {
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

/* Mobile responsive styles for horizontal layout */
@media (max-width: 768px) {
  .input-horizontal-row {
    padding: 6px 8px !important;
    gap: 6px !important;
  }

  .input-horizontal-row .left-actions {
    gap: 4px !important;
  }

  .input-horizontal-row .input-main-area textarea {
    font-size: 0.95rem !important;
    padding: 6px 8px !important;
  }

  .action-btn {
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    font-size: 16px !important;
  }

  .send-btn {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
  }
}

@media (max-width: 480px) {
  .input-horizontal-row {
    padding: 4px 6px !important;
    gap: 4px !important;
  }

  .input-horizontal-row .input-main-area textarea {
    font-size: 0.9rem !important;
    padding: 4px 6px !important;
  }

  .action-btn {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    min-height: 18px !important;
    font-size: 14px !important;
    margin: 0 2px 0 0 !important;
  }

  .send-btn {
    width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
    min-height: 26px !important;
    margin: 0 0 0 4px !important;
  }
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn-icon {
    width: 16px;
    height: 16px;
    display: block;
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.04);
    color: var(--accent-color);
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    background: transparent;
    border: none;
}

.scroll-up-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--cta-color);
    border: none;
    color: white;
    cursor: pointer;
    box-shadow: none; /* Removed shadow */
    transition: all 0.2s;
}

.scroll-up-btn:hover {
    background: var(--cta-hover);
    transform: translateY(-1px);
    box-shadow: none; /* Removed shadow */
}

.input-wrapper:focus-within {
    border-color: var(--accent-color);
    box-shadow: none; /* Removed shadow */
}

.theme-dark .chat-input-container {
    background-color: var(--input-bg); /* Dark gray for input background */
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .input-main-area,
.theme-dark .input-actions-row {
    background-color: var(--input-bg); /* Dark gray for input background */
}

.theme-dark .input-wrapper {
    box-shadow: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .input-wrapper:focus-within {
    box-shadow: none;
    border-color: rgba(255, 255, 255, 0.2);
}

textarea {
    flex: 1;
    background-color: var(--input-bg) !important; /* Ensure opaque background for textarea */
    border: none;
    padding: 12px 16px;
    color: var(--input-text);
    font-size: 1rem;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
    resize: none;
    outline: none;
    max-height: 120px;
    min-height: 24px;
    overflow-y: auto;
}

textarea::placeholder {
    color: var(--input-placeholder);
}

.input-actions {
    display: flex;
    align-items: center;
    padding-right: 8px;
    gap: 4px;
}

.input-action-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.2s;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.input-action-btn:hover {
    color: var(--accent-color);
}

.send-btn {
    background-color: var(--cta-color);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 6px; /* Slightly rounded square button */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    margin-right: 0;
    transition: all 0.2s;
    box-shadow: none;
    padding: 0;
    overflow: hidden;
}

.send-btn:hover {
    background-color: var(--cta-hover);
    transform: none;
    box-shadow: none;
}

.send-btn:disabled {
    background-color: var(--button-disabled);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* Custom button icons */
.send-icon {
    width: 16px;
    height: 16px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    line-height: 0;
}

.loading-icon {
    width: 16px;
    height: 16px;
    display: none;
    animation: rotate 1s linear infinite;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    line-height: 0;
}

@keyframes rotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Input footer removed as per user request */
.input-footer {
    display: none;
}

/* File display area */
.file-display-area {
    display: none; /* Hide by default, will be shown when files are added */
    overflow-x: auto;
    padding: 16px;
    gap: 8px;
    border-top: 1px solid var(--border-color);
    max-width: 100%;
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.file-display-area::-webkit-scrollbar {
    display: none;
}

.file-card {
    min-width: 150px;
    max-width: 200px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-secondary);
}

.file-card .file-name {
    font-weight: 500;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-card .file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-card .file-lines {
    font-size: 12px;
    color: var(--text-secondary);
}

.file-card .file-type {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

/* Document in message input */
@keyframes documentPulse {
    0% { opacity: 0.9; }
    50% { opacity: 1; }
    100% { opacity: 0.9; }
}

@keyframes documentPulseDark {
    0% { opacity: 0.9; }
    50% { opacity: 1; }
    100% { opacity: 0.9; }
}

.theme-dark .input-document {
    border: 1px solid rgba(79, 139, 249, 0.4);
    background-color: rgba(22, 32, 66, 0.8);
    box-shadow: none; /* Removed shadow */
    animation: fadeIn 0.3s ease;
}

.input-document {
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    width: 100%;
    position: relative;
    box-shadow: none; /* Removed shadow */
    max-width: 100%;
    animation: fadeIn 0.3s ease;
}

.document-icon {
    width: 32px;
    height: 32px;
    background-color: #333333; /* Dark gray for light mode */
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 10px;
    flex-shrink: 0;
    font-size: 14px;
}

.theme-dark .document-icon {
    background-color: #666666; /* Medium gray for dark mode */
    box-shadow: none; /* Removed shadow */
}

.document-info {
    flex: 1;
    overflow: hidden;
}

.document-name {
    font-weight: 600;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
    font-size: 14px;
}

.document-type {
    font-size: 12px;
    color: var(--text-secondary);
}

.document-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #000000;
    border: none;
    color: white;
    cursor: pointer;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    transition: background-color 0.2s;
    padding: 0;
    line-height: 1;
    z-index: 10;
}

.document-remove:hover {
    background-color: #333333;
}

/* Typing indicator */
.typing-indicator {
    padding: 16px 30px;
}

.typing-dots {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
    height: 24px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    display: inline-block;
    animation: typingDot 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.6;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Source Panel */
.source-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: 100vh;
    background-color: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    z-index: 30;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: none; /* Hide the source panel completely */
    flex-direction: column;
    box-shadow: -2px 0 10px var(--shadow-color);
}

.source-panel.active {
    transform: translateX(0);
    display: none; /* Keep it hidden even when active */
}

.source-panel-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.source-panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.source-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.source-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.source-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.source-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.source-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--accent-color);
}

.source-content {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
}

.icon-button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-button:hover {
    color: var(--accent-color);
}

/* Voice Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 40;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px; /* Reduced from 800px to 500px */
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-dark .modal-content {
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(10, 16, 33, 0.5);
    background-color: var(--bg-primary); /* Dark slate background for dark mode */
}

/* Settings modal specific styling */
#settingsModal {
    z-index: 9999 !important; /* Very high z-index to ensure it's above all other elements */
    background-color: rgba(0, 0, 0, 0.7) !important; /* Darker background for better contrast */
    backdrop-filter: blur(3px) !important; /* Add blur effect to background */
}

#settingsModal .modal-content {
    width: 95%; /* Increased width to allow main area to expand */
    max-width: 840px; /* Reduced max-width by 30% from 1200px */
    height: 650px; /* Made slightly less lengthy */
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
    z-index: 10000 !important; /* Even higher z-index for the content */
}

#settingsModal .settings-sidebar {
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
}

.theme-dark #settingsModal .modal-content {
    background-color: #1E1E1E; /* Dark background for main chat area */
}

.theme-dark #settingsModal .settings-sidebar {
    background-color: #2B2B2B; /* Dark gray for sidebar */
    border-right: 1px solid #444654;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.theme-dark .modal-header {
    background-color: #2B2B2B; /* Dark gray for sidebar */
    border-bottom: 1px solid #444654;
}

#voiceModal .modal-header {
    padding: 10px 16px; /* Reduced padding */
}

#voiceModal .icon-button {
    color: #000000;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-body {
    padding: 0;
    display: flex;
    height: auto; /* Changed from fixed 400px */
    overflow: hidden;
    flex: 1;
}

/* Voice modal specific styling */
#voiceModal .modal-body {
    padding: 16px;
    display: block;
    max-height: 300px; /* Reduced height */
    overflow-y: auto;
}

/* Settings Modal Styles */
.settings-layout {
    display: flex;
    width: 100%;
    height: 100%;
}

.settings-sidebar {
    width: 200px; /* Kept sidebar width same */
    border-right: 1px solid var(--border-color);
    padding: 0;
    background-color: #f7f7f8;
    overflow-y: auto;
    flex-shrink: 0;
}

.settings-nav-item {
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
    font-size: 14px;
    transition: background-color 0.2s;
    margin-bottom: 0;
    border-radius: 8px;
    margin: 4px 8px;
}

.settings-nav-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.theme-dark .settings-nav-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.settings-nav-item.active {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: 600;
    position: relative;
}

.theme-dark .settings-nav-item.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.settings-nav-item i {
    width: 16px;
    text-align: center;
}

.settings-content {
    flex: 1; /* Allow main content to take up remaining space */
    padding: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.theme-dark .settings-content {
    background-color: #1E1E1E; /* Dark background for main chat area */
    color: var(--text-primary);
}

.settings-panel {
    display: none;
    padding: 0;
    height: 100%;
    flex: 1;
    background-color: white;
}

.theme-dark .settings-panel {
    background-color: #1E1E1E; /* Dark background for main chat area */
}

.settings-panel.active {
    display: block;
}

/* New settings item style to match the example */
.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 32px;
    border-bottom: 1px solid var(--border-color);
    min-height: 60px;
    box-sizing: border-box;
}

.theme-dark .settings-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-item-left {
    font-size: 16px;
    color: var(--text-primary);
    font-weight: 400;
    max-width: 70%;
    line-height: 1.4;
}

.settings-item-right {
    display: flex;
    align-items: center;
}

.settings-description {
    padding: 0 32px 16px;
    border-bottom: 1px solid var(--border-color);
    box-sizing: border-box;
}

.theme-dark .settings-description {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-description p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Dropdown select styling */
.dropdown-select {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-primary);
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 6px 30px 6px 14px;
    width: 100%;
    max-width: 150px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    text-align: left;
    font-weight: 500;
}

.dropdown-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.theme-dark .dropdown-select {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.theme-dark .dropdown-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.dropdown-select option {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dropdown-select i {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Toggle switch styling to match the example */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .2s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .2s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
    background-color: #10a37f; /* ChatGPT green color */
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Switch styling */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* Button styling */
.settings-button {
    padding: 8px 16px;
    border-radius: 6px;
    background-color: white;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 100px;
    text-align: center;
    font-weight: 500;
}

.settings-button:hover {
    background-color: var(--bg-secondary);
}

.logout-button {
    border-radius: 6px;
}

/* Dropdown select styling */
.dropdown-select {
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--text-primary);
    font-size: 14px;
    min-width: 120px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 32px;
}

.theme-dark .dropdown-select {
    background-color: #343541; /* Dark gray for input background */
    color: #FFFFFF;
    border-color: #444654;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

.theme-dark .settings-button {
    background-color: #343541; /* Dark gray for input background */
    color: #FFFFFF;
    border-color: #444654;
}

.theme-dark .settings-button:hover {
    background-color: #444654; /* Slightly lighter on hover */
}

.settings-button.danger {
    color: #fff;
    border-color: #ef4146;
    background-color: #ef4146;
    border-radius: 6px;
    font-weight: 500;
}

.settings-button.danger:hover {
    background-color: #d63031;
    border-color: #d63031;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

#voiceModal .modal-footer {
    padding: 12px 16px; /* Reduced padding */
}

/* Voice modal specific button styling */
#voiceModal .btn {
    min-width: 80px;
    font-weight: 500;
}

#voiceModal .btn-primary {
    background-color: #f5f5f5;
    color: #000000;
}

#voiceModal .btn-primary:disabled {
    opacity: 0.5;
    background-color: #f5f5f5;
    color: #888888;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

.btn-primary {
    background-color: var(--cta-color);
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.25);
    transition: all 0.2s;
}

.btn-primary:hover {
    background-color: var(--cta-hover);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(255, 107, 107, 0.3);
}

.btn-primary:disabled {
    background-color: var(--button-disabled);
    cursor: not-allowed;
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-secondary);
}

.recording-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px; /* Further reduced from 16px */
}

#recordingStatus {
    font-size: 14px;
    margin: 0;
    font-weight: 500;
    color: #000000; /* Changed to black */
}

.recording-waves {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px; /* Further reduced from 40px */
    margin-bottom: 6px; /* Further reduced from 10px */
}

.recording-waves span {
    display: inline-block;
    width: 5px;
    height: 5px;
    margin: 0 2px;
    background-color: #000000; /* Changed to black */
    border-radius: 50%;
    animation: recording 1.5s infinite ease-in-out;
}

.theme-dark .recording-waves span {
    background-color: #ffffff; /* White for dark theme */
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 5px rgba(255, 255, 255, 0.4);
}

/* Add active class for recording waves */
.recording-active .recording-waves span {
    animation-play-state: running;
}

.recording-waves span:nth-child(1) {
    animation-delay: 0s;
}

.recording-waves span:nth-child(2) {
    animation-delay: 0.2s;
    animation-duration: 1.8s;
}

.recording-waves span:nth-child(3) {
    animation-delay: 0.4s;
    animation-duration: 1.6s;
}

.recording-waves span:nth-child(4) {
    animation-delay: 0.6s;
    animation-duration: 1.4s;
}

.recording-waves span:nth-child(5) {
    animation-delay: 0.8s;
    animation-duration: 1.2s;
}

@keyframes recording {
    0%, 100% {
        height: 3px;
        transform: scaleY(1);
    }
    50% {
        height: 15px; /* Further reduced from 20px */
        transform: scaleY(1.5);
    }
}

.recording-controls {
    display: flex;
    justify-content: center;
    gap: 12px; /* Reduced from 16px */
    margin-bottom: 16px; /* Reduced from 24px */
}

.record-btn {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: none;
    padding: 6px 12px; /* Reduced from 8px 16px */
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px; /* Reduced from 8px */
    transition: all 0.2s;
    font-size: 13px; /* Added smaller font size */
    position: relative;
    overflow: hidden;
}

.record-btn-icon {
    width: 14px;
    height: 14px;
    display: inline-block;
}

/* Make the icon black in the Start button */
.record-start-btn .record-btn-icon {
    filter: brightness(0); /* This makes the icon black */
}

.record-btn:hover {
    background-color: var(--border-color);
}

.record-btn:active {
    background-color: var(--accent-color);
    color: white;
    transform: scale(0.98);
}

.record-btn:focus {
    outline: 2px solid rgba(0, 0, 0, 0.1);
    outline-offset: 1px;
}

/* Specific styling for start and stop buttons */
.record-start-btn {
    background-color: #f5f5f5;
    border: 1px solid #d0d0d0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.record-start-btn:hover {
    background-color: #e8e8e8;
    border-color: #c0c0c0;
}

.record-start-btn:active, .record-start-btn.active {
    background-color: #e0e0e0;
    border-color: #b0b0b0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    color: #000000;
}

.record-stop-btn {
    background-color: #f5f5f5;
    border: 1px solid #d0d0d0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.record-stop-btn:hover:not(:disabled) {
    background-color: #e8e8e8;
    border-color: #c0c0c0;
}

.record-stop-btn:active:not(:disabled), .record-stop-btn.active:not(:disabled) {
    background-color: #e0e0e0;
    border-color: #b0b0b0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    color: #000000;
}

.record-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.transcription-result {
    background-color: var(--bg-secondary);
    padding: 12px;
    border-radius: 8px;
    min-height: 40px; /* Further reduced from 60px */
    max-height: 80px; /* Further reduced from 120px */
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid var(--border-color);
    outline: none;
    cursor: text;
    transition: border-color 0.2s;
}

.transcription-result:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.theme-dark .transcription-result {
    background-color: rgba(22, 32, 66, 0.8);
    border: 1px solid rgba(79, 139, 249, 0.3);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-dark .transcription-result:focus {
    border-color: rgba(79, 139, 249, 0.7);
    box-shadow: 0 0 0 2px rgba(79, 139, 249, 0.25);
}

.transcription-result:empty:before {
    content: "Your speech will appear here. You can edit it before submitting.";
    color: var(--input-placeholder);
    font-style: italic;
}

/* Confirmation Modal */
.confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.confirmation-modal.show {
    opacity: 1;
}

.confirmation-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
}

.confirmation-content h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
}

.confirmation-content p {
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
}

.confirmation-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.theme-dark .confirmation-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
}

/* Toast Notification */
.simple-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background-color: #333333; /* Default background */
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 80%;
    text-align: center;
}

.simple-toast.show {
    transform: translateX(-50%) translateY(0);
    transition: transform 0.3s ease;
}

.toast-close-btn {
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    margin-left: 16px;
    opacity: 0.8;
}

.toast-close-btn:hover {
    opacity: 1;
}

.theme-dark .simple-toast {
    background-color: #4a5568;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Success toast */
.simple-toast.success {
    background-color: #4CAF50; /* Green for success */
    border-left: 4px solid #388E3C; /* Darker green border */
}

/* Error toast */
.simple-toast.error {
    background-color: #F44336; /* Red for error */
    border-left: 4px solid #D32F2F; /* Darker red border */
}

/* Info toast */
.simple-toast.info {
    background-color: #333333; /* Gray for info */
    border-left: 4px solid #222222; /* Darker gray border */
}

.theme-dark .simple-toast.success {
    background-color: #4CAF50; /* Keep green in dark mode */
    border-left: 4px solid #388E3C; /* Darker green border */
}

.theme-dark .simple-toast.error {
    background-color: #F44336; /* Keep red in dark mode */
    border-left: 4px solid #D32F2F; /* Darker red border */
}

.theme-dark .simple-toast.info {
    background-color: #343541; /* Dark gray for input background */
    border-left: 4px solid #444654; /* Slightly lighter border */
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        transform: translateX(-100%);
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        width: var(--sidebar-width) !important; /* Force width on mobile */
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .sidebar.collapsed {
        transform: translateX(-100%);
    }

    .sidebar-close {
        display: block;
    }

    /* Position toggle button in header on mobile */
    .sidebar-toggle {
        position: fixed;
        top: 14px;
        left: 14px;
        color: var(--text-primary);
        background-color: transparent;
        border: none;
        z-index: 15;
        box-shadow: none;
    }

    /* Ensure chat elements remain centered */
    .chat-messages,
    .welcome-message {
        width: 90%;
        max-width: 90%;
        margin: 0 auto;
        align-self: center;
    }

    .chat-input-container {
        width: 90%;
        max-width: 90%;
        left: 50% !important; /* Force centering */
        transform: translate(-50%, 0) !important; /* Center horizontally only */
        margin: 20px auto 0 !important; /* Add margin to position below welcome message */
        right: auto !important; /* Prevent right positioning from affecting centering */
        position: relative !important; /* Ensure relative positioning */
        bottom: auto !important; /* Override bottom positioning */
        top: auto !important; /* Override top positioning */
    }

    .greeting-message-container {
        width: 90%;
        max-width: 90%;
        margin: 0;
        padding: 10px 15px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10; /* Ensure it's above other elements */
        position: relative; /* Needed for z-index to work */
    }
}

@media (max-width: 768px) {
    .sidebar.active .sidebar-toggle {
        color: var(--sidebar-text);
        background-color: transparent;
        border: none;
        position: relative;
        top: auto;
        left: auto;
        box-shadow: none;
    }
}

@media (max-width: 768px) {
    /* Ensure sidebar title is visible and properly positioned */
    .sidebar-title {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Ensure action buttons are visible on mobile and aligned to the right */
    .sidebar.active .sidebar-action-buttons {
        display: flex;
        position: relative;
        top: auto;
        right: auto;
    }

    /* Ensure top controls maintain proper layout */
    .sidebar-top-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 14px;
    }
}

@media (max-width: 576px) {
    .chat-input-container {
        padding: 12px;
        width: 95%; /* Wider on small screens */
        max-width: 95%; /* Wider on small screens */
        left: 50% !important; /* Force centering */
        margin: 0 !important; /* Remove margin */
        right: auto !important; /* Prevent right positioning from affecting centering */
        position: fixed !important; /* Fixed positioning */
        top: 65% !important; /* Position at 65% from the top */
        bottom: auto !important; /* Override bottom positioning */
        transform: translate(-50%, -50%) !important; /* Center both horizontally and vertically */
    }

    .welcome-message {
        padding: 16px;
        width: 95%; /* Wider on small screens */
        max-width: 95%; /* Wider on small screens */
        margin: 0 auto; /* Center horizontally */
    }

    .message {
        padding: 16px;
        width: 95%; /* Wider on small screens */
        max-width: 95%; /* Wider on small screens */
    }

    .chat-messages {
        width: 95%; /* Wider on small screens */
        max-width: 95%; /* Wider on small screens */
        margin: 0 auto;
        align-self: center;
    }

    .greeting-message-container {
        width: 95%; /* Wider on small screens */
        max-width: 95%; /* Wider on small screens */
        margin: 0;
        padding: 8px 15px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10; /* Ensure it's above other elements */
        position: relative; /* Needed for z-index to work */
        display: block !important; /* Force display */
        opacity: 1 !important; /* Force opacity */
    }

    .source-panel {
        width: 80%;
    }

    .modal-content {
        width: 95%;
    }
}

/* Add event listener for theme changes to update the new chat button icon */
document.addEventListener('themeChanged', function(e) {
// ... existing code ...
});

/* Existing styles for message feedback buttons */
.message-feedback .feedback-btn {
    background-color: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    opacity: 0.7;
    transition: opacity 0.2s ease, transform 0.1s ease; /* Added transform to transition */
    padding: 4px;
    margin: 0 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.message-feedback .feedback-btn:hover {
    opacity: 1;
    background-color: var(--feedback-hover-bg); /* Add a hover background */
}

.message-feedback .feedback-btn:active {
    transform: scale(0.95); /* Slightly scale down on click */
}

/* Animation Keyframes */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate(0,0); }
    40%, 43% { transform: translate(0, -5px); }
    78% { transform: translate(0, -2px); }
}

@keyframes quick-highlight {
    0% { background-color: transparent; }
    50% { background-color: rgba(0, 123, 255, 0.3); }
    100% { background-color: transparent; }
}

/* Specific button animations */
.message-feedback .copy-btn:active {
    animation: quick-highlight 0.5s ease; /* Apply quick highlight on active */
}

.message-feedback .thumbs-up.active,
.message-feedback .thumbs-down.active {
    animation: bounce 0.6s ease; /* Apply bounce on active state */
}

.message-feedback .audio-btn.active {
    animation: pulse 1s infinite ease-in-out; /* Apply pulse on active state */
}

/* Ensure icons scale with the button */
.message-feedback .feedback-btn i {
    pointer-events: none; /* Ensure clicks go to the button */
}


/* Existing styles for message footer */
.message-footer {
// ... existing code ...
}

/* Styles for the inline escalation icon */
#inlineEscalationIconContainer {
    /* Remove the inline display: none set in HTML */
    display: none; /* Initially hidden, will be set to flex by JS */
    align-items: center;
    justify-content: center;
    padding: 8px; /* Match padding of other icon buttons */
    margin-left: 5px; /* Space from the microphone icon */
    margin-right: -5px; /* Pull slightly closer to the send button */
    cursor: pointer;
    /* Subtle styling */
    opacity: 0.7; /* Slightly transparent */
    transition: opacity 0.2s ease;
}

#inlineEscalationIconContainer:hover {
    opacity: 1; /* Full opacity on hover */
}

#inlineEscalationIconContainer i {
    font-size: 16px; /* Match other icons */
    /* Subtle color - muted orange/red */
    color: #FF6347; /* Tomato - subtle red/orange */
}

.theme-dark #inlineEscalationIconContainer i {
    /* Subtle color in dark mode */
    color: #FF7F50; /* Coral - slightly lighter in dark mode */
}

/* Ensure the icon button container uses flex to align items */
.chat-input-controls {
    display: flex;
    align-items: center;
}

/* Adjust margin for the send button to account for the new icon */
.chat-input-controls #sendBtn {
    margin-left: 5px;
}

/* Media query for smaller screens if needed */
@media (max-width: 768px) {
    #inlineEscalationIconContainer {
        padding: 6px; /* Slightly smaller padding on mobile */
        margin-left: 3px; /* Slightly smaller margin */
        margin-right: -3px;
    }

    #inlineEscalationIconContainer i {
        font-size: 14px; /* Slightly smaller icon on mobile */
    }
}

.settings-item-right {
    display: flex;
    align-items: center;
}

/* Style for input fields within settings items */
.settings-item-right input[type="text"],
.settings-item-right input[type="email"] {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-size: 14px; /* Adjusted font size */
    transition: all 0.2s ease;
    background-color: var(--input-bg);
    color: var(--text-primary); /* Ensure text color is readable */
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
}

.theme-dark .settings-item-right input[type="text"],
.theme-dark .settings-item-right input[type="email"] {
    background-color: var(--input-bg) !important; /* Use dark mode input background and add !important */
    color: var(--text-primary) !important; /* Use dark mode primary text color and add !important */
    border-color: var(--input-border) !important; /* Use dark mode input border and add !important */
}

/* Ensure chat input is fixed at the bottom and centered horizontally on small screens */
@media (max-width: 768px) {
    .chat-input-container {
        position: fixed !important;
        bottom: 24px !important;
        top: auto !important;
        left: 50% !important;
        transform: translateX(-50%) !important; /* Ensure horizontal centering */
        margin: 0 auto !important;
        right: auto !important;
        width: 90%; /* Keep the specified width */
        max-width: 90%; /* Keep the specified max-width */
        padding: 12px; /* Keep the specified padding */
    }
}

@media (max-width: 576px) {
    .chat-input-container {
        position: fixed !important;
        bottom: 24px !important;
        top: auto !important;
        left: 50% !important;
        transform: translateX(-50%) !important; /* Ensure horizontal centering */
        margin: 0 auto !important;
        right: auto !important;
        width: 95%; /* Keep the specified width */
        max-width: 95%; /* Keep the specified max-width */
        padding: 12px; /* Keep the specified padding */
    }
}

/* --- Enhanced Responsive Design for All Devices --- */

@media (max-width: 1200px) {
  .app-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  .sidebar {
    min-width: 180px;
    max-width: 220px;
  }
}

@media (max-width: 992px) {
  .sidebar {
    position: absolute;
    z-index: 1002;
    height: 100vh;
    left: 0;
    top: 0;
    width: 80vw;
    max-width: 320px;
    min-width: 0;
    box-shadow: 2px 0 8px rgba(0,0,0,0.08);
    background: var(--sidebar-bg);
  }
  .sidebar.collapsed {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
  }
  .app-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  .chat-container {
    width: 100vw;
    min-width: 0;
    margin: 0;
    padding: 0;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 90vw;
    max-width: 320px;
    min-width: 120px;
    left: -100vw;
    top: 0;
    height: 100vh;
    z-index: 2000;
    transition: left 0.3s;
  }
  .sidebar.active {
    left: 0;
  }
  .chat-container {
    width: 100vw;
    min-width: 0;
    margin: 0;
    padding: 0;
  }
  .chat-header, .header-content, .header-left, .header-brand {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 8px;
  }
  .greeting-message-container, .welcome-container, .welcome-message {
    max-width: 98vw;
    width: 98vw;
    padding: 10px 2vw;
    font-size: 1rem;
  }
  .suggestion-chips, .chip {
    flex-wrap: wrap;
    gap: 8px;
    font-size: 0.95rem;
  }
  .chat-input-container, .input-main-area, .input-actions-row {
    width: 100vw;
    min-width: 0;
    padding: 0 2vw;
    box-sizing: border-box;
  }
  .chat-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    max-width: 100vw;
  }
  .chat-messages {
    padding-bottom: 80px;
    max-width: 100vw;
    overflow-x: hidden;
  }
  .modal-content, .modal-lg {
    max-width: 98vw !important;
    width: 98vw !important;
    min-width: 0;
    max-height: 98vh !important;
    overflow-y: auto;
    border-radius: 10px;
    padding: 8px 4px;
  }
}

@media (max-width: 576px) {
  .sidebar {
    width: 100vw;
    min-width: 0;
    max-width: 100vw;
    left: -100vw;
    top: 0;
    height: 100vh;
    z-index: 2000;
    transition: left 0.3s;
  }
  .sidebar.active {
    left: 0;
  }
  .chat-container, .chat-header, .header-content, .header-left, .header-brand {
    width: 100vw;
    min-width: 0;
    padding: 0 2vw;
    box-sizing: border-box;
  }
  .greeting-message-container, .welcome-container, .welcome-message {
    max-width: 100vw;
    width: 100vw;
    padding: 6px 1vw;
    font-size: 0.95rem;
  }
  .suggestion-chips, .chip {
    flex-wrap: wrap;
    gap: 6px;
    font-size: 0.9rem;
  }
  .chat-input-container, .input-main-area, .input-actions-row {
    width: 100vw;
    min-width: 0;
    padding: 0 1vw;
    box-sizing: border-box;
  }
  .chat-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    max-width: 100vw;
  }
  .chat-messages {
    padding-bottom: 90px;
    max-width: 100vw;
    overflow-x: hidden;
  }
  .modal-content, .modal-lg {
    max-width: 100vw !important;
    width: 100vw !important;
    min-width: 0;
    max-height: 100vh !important;
    overflow-y: auto;
    border-radius: 8px;
    padding: 4px 2px;
  }
  .sidebar-close {
    display: block !important;
  }
}

/* Ensure modals and overlays are always visible and scrollable on all devices */
.modal, .modal-content, .modal-lg {
  max-width: 100vw;
  max-height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}

/* Fix for chat input and suggestion chips on very small screens */
@media (max-width: 400px) {
  .chat-input-container, .input-main-area, .input-actions-row, .greeting-message-container, .welcome-message {
    font-size: 0.85rem;
    padding: 0 1vw;
  }

}

/* --- GLOBAL: box-sizing and overflow management --- */
html, body {
  box-sizing: border-box;
  overflow: hidden;
  width: 100vw;
  max-width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.app-container {
  box-sizing: border-box;
  overflow: hidden;
  width: 100vw;
  max-width: 100vw;
  height: 100vh;
  display: flex;
}

.chat-container {
  box-sizing: border-box;
  overflow: hidden;
  flex: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  padding-bottom: 120px;
}

* {
  box-sizing: inherit;
}

/* --- MAIN LAYOUT --- */
.app-container {
  width: 100vw;
  max-width: 100vw;
  min-width: 0;
  height: 100vh;
  display: flex;
  overflow: hidden;
}

.chat-container {
  flex: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Sidebar layout */
.sidebar {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar-conversations {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* --- WELCOME/GREETING CARD --- */
.greeting-message-container {
  width: 100%;
  max-width: 480px;
  min-width: 0;
  margin: 0 auto;
  padding: 6vw 4vw 4vw 4vw;
  box-sizing: border-box;
  text-align: center;
  word-break: break-word;
}
@media (max-width: 576px) {
  .greeting-message-container {
    max-width: 98vw;
    padding: 6vw 2vw 4vw 2vw;
    font-size: 1rem;
  }
}
@media (max-width: 400px) {
  .greeting-message-container {
    max-width: 100vw;
    padding: 4vw 1vw 2vw 1vw;
    font-size: 0.95rem;
  }
}



/* --- CHAT INPUT BAR --- */
.chat-input-container {
  width: 100vw;
  max-width: 100vw;
  left: 0;
  right: 0;
  bottom: 0;
  position: fixed;
  z-index: 1001;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 0 4vw 4vw 4vw;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.chat-input-form {
  width: 100%;
  max-width: 600px;
  min-width: 0;
  box-sizing: border-box;
  margin: 0 auto;
  background: var(--input-bg);
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  border: 1px solid var(--input-border);
  overflow: hidden;
  display: flex;
  flex-direction: row;
}
.input-main-area textarea {
  width: 100%;
  min-width: 0;
  max-width: 100vw;
  font-size: 1rem;
  padding: 3vw 2vw;
  border-radius: 12px 12px 0 0;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  min-height: 36px;
  max-height: 120px;
  box-sizing: border-box;
}
@media (max-width: 576px) {
  .chat-input-container {
    padding: 0 2vw 2vw 2vw;
  }
  .chat-input-form {
    max-width: 98vw;
  }
  .input-main-area textarea {
    font-size: 0.95rem;
    padding: 4vw 2vw;
    min-height: 32px;
    max-height: 100px;
  }
}
@media (max-width: 400px) {
  .chat-input-container {
    padding: 0 1vw 1vw 1vw;
  }
  .chat-input-form {
    max-width: 100vw;
  }
  .input-main-area textarea {
    font-size: 0.9rem;
    padding: 5vw 1vw;
  }
}

.input-actions-row {
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1vw 2vw 2vw 2vw;
  gap: 2vw;
}

/* --- Prevent horizontal scroll on all elements --- */
body, html {
  overflow-x: hidden !important;
}

.greeting-message-container, .welcome-container {
    padding: 0 !important;
    max-width: 600px !important;
    min-width: 340px !important;
    width: 80vw !important;
    margin: 16px auto 4px auto !important;
    height: auto !important;
    background: none !important;
    box-shadow: none !important;
    border: none !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* REMOVED - Conflicting rule that overrides premium styling */

.chat-input-container {
    padding: 4px 24px !important;
    max-width: 560px !important;
    min-width: 320px !important;
    width: 80vw !important;
    border-radius: 5px !important;
    bottom: 12px !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 auto !important;
    background: #fff !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03) !important;
    border: 1px solid #bdbdbd !important;
    height: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.suggestion-chips {
    gap: 8px !important;
    margin-top: 8px !important;
    justify-content: center !important;
    width: 100% !important;
    display: flex !important;
    flex-wrap: wrap !important;
}



.suggestion-chip i, .suggestion-chip svg, .suggestion-chip img {
    width: 18px !important;
    height: 18px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

@media (max-width: 700px) {
    .greeting-message-container, .welcome-container, .welcome-message, .chat-input-container {
        max-width: 98vw !important;
        min-width: 0 !important;
        width: 98vw !important;
        padding-left: 2vw !important;
        padding-right: 2vw !important;
    }

}

.greeting-message-container, .welcome-container {
    max-width: 720px !important;
    min-width: 340px !important;
    width: 90vw !important;
}

/* REMOVED - Conflicting rule */

.chat-input-container {
    max-width: 680px !important;
    min-width: 320px !important;
    width: 90vw !important;
    height: auto !important;
    min-height: 60px !important;
    padding: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.suggestion-chips {
    justify-content: center !important;
    width: 100% !important;
    gap: 8px !important;
}



.suggestion-chip i, .suggestion-chip svg, .suggestion-chip img {
    width: 18px !important;
    height: 18px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}



.chat-input-form {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: center !important;
}

/* EXACT ChatGPT Input Container - Pixel Perfect Replica */
.chat-input-container {
    position: absolute !important;
    top: 70% !important;
    bottom: auto !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 100% !important;
    max-width: 768px !important;
    padding: 0 20px !important;
    z-index: 1000 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.chat-input-form {
    width: 100% !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.chatgpt-input-wrapper {
    display: flex !important;
    flex-direction: column !important;
    background: #ffffff !important;
    border: 1px solid #d1d5db !important;
    border-radius: 26px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    padding: 16px !important;
    gap: 12px !important;
    min-height: 80px !important;
    transition: border-color 0.15s ease, box-shadow 0.15s ease !important;
    position: relative !important;
    overflow: visible !important;
    z-index: 1 !important;
}

/* Placeholder for gradient border styles - moved to end of file for higher priority */

/* Enhanced focus state with subtle glow when text is present */
.chatgpt-input-wrapper.has-text:focus-within {
    border-color: transparent !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Normal focus state when no text */
.chatgpt-input-wrapper:focus-within {
    border-color: #9ca3af !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Gradient animation keyframes moved to end of file for higher priority */

/* Remove old tools section styles - replaced with bottom tools */

/* Main Input Area - Top Section */
.chatgpt-input-area {
    width: 100% !important;
    order: 1 !important;
}

.chatgpt-input-area textarea {
    width: 100% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    resize: none !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    font-size: 16px !important;
    line-height: 24px !important;
    color: #374151 !important;
    padding: 0 !important;
    margin: 0 !important;
    min-height: 24px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.chatgpt-input-area textarea::-webkit-scrollbar {
    display: none !important;
}

.chatgpt-input-area textarea::placeholder {
    color: #9ca3af !important;
    font-size: 16px !important;
}

/* Bottom Tools Section */
.chatgpt-bottom-tools {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    order: 2 !important;
}

.chatgpt-left-tools {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.chatgpt-right-tools {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.chatgpt-tool-btn {
    border: none !important;
    background: transparent !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    cursor: pointer !important;
    color: #6b7280 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 6px 8px !important;
    transition: background-color 0.15s ease, color 0.15s ease !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    height: 32px !important;
    min-width: 32px !important;
}

.chatgpt-tool-btn:hover {
    background-color: #f3f4f6 !important;
    color: #374151 !important;
}

.chatgpt-tool-btn:active {
    background-color: #e5e7eb !important;
}

.chatgpt-tool-btn i {
    font-size: 14px !important;
}

.chatgpt-tool-btn span {
    font-size: 14px !important;
    font-weight: 500 !important;
}

/* Arrow controls styling (hidden but functional) */
.arrow-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 1px !important;
}

.chatgpt-arrow-btn {
    width: 20px !important;
    height: 16px !important;
    border: none !important;
    background: transparent !important;
    border-radius: 4px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    color: #8e8ea0 !important;
    font-size: 10px !important;
    transition: background-color 0.15s ease, color 0.15s ease !important;
    padding: 0 !important;
}

.chatgpt-arrow-btn:hover {
    background-color: #f1f1f1 !important;
    color: #565869 !important;
}

.chatgpt-arrow-btn:active {
    background-color: #e8e8e8 !important;
}

/* Send Button - Bottom Right */
.chatgpt-send-btn {
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    background: #2d333a !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    color: #ffffff !important;
    transition: background-color 0.15s ease !important;
    padding: 0 !important;
}

.chatgpt-send-btn:hover:not(:disabled) {
    background: #1a1a1a !important;
}

.chatgpt-send-btn:disabled {
    background: #d1d5db !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
}

.chatgpt-send-btn svg {
    width: 16px !important;
    height: 16px !important;
}

/* Mobile Responsive - Vertical Layout */
@media (max-width: 768px) {
    .chat-input-container {
        bottom: 10px !important;
        padding: 0 16px !important;
        max-width: 100% !important;
    }

    .chatgpt-input-wrapper {
        padding: 14px !important;
        min-height: 70px !important;
        border-radius: 22px !important;
        gap: 10px !important;
    }

    /* Adjust gradient border for mobile */
    .chatgpt-input-wrapper::before {
        border-radius: 24px;
    }

    .chatgpt-tool-btn {
        height: 28px !important;
        min-width: 28px !important;
        font-size: 13px !important;
        padding: 5px 6px !important;
    }

    .chatgpt-send-btn {
        width: 28px !important;
        height: 28px !important;
    }

    .chatgpt-send-btn svg {
        width: 14px !important;
        height: 14px !important;
    }

    .chatgpt-input-area textarea {
        font-size: 16px !important; /* Prevent zoom on iOS */
        line-height: 22px !important;
    }

    .chatgpt-left-tools {
        gap: 6px !important;
    }

    .chatgpt-right-tools {
        gap: 6px !important;
    }
}

@media (max-width: 480px) {
    .chat-input-container {
        bottom: 8px !important;
        padding: 0 12px !important;
    }

    .chatgpt-input-wrapper {
        padding: 12px !important;
        gap: 8px !important;
        min-height: 64px !important;
        border-radius: 20px !important;
    }

    /* Adjust gradient border for smaller screens */
    .chatgpt-input-wrapper::before {
        border-radius: 22px;
    }

    .chatgpt-left-tools {
        gap: 4px !important;
    }

    .chatgpt-right-tools {
        gap: 4px !important;
    }
}

/* Override all previous chat-input-container styles for ChatGPT design */
body .chat-input-container,
.app-container .chat-input-container,
.chat-container .chat-input-container,
.sidebar.collapsed ~ .chat-container .chat-input-container,
.sidebar:not(.collapsed) ~ .chat-container .chat-input-container {
    position: absolute !important;
    top: 70% !important;
    bottom: auto !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 100% !important;
    max-width: 768px !important;
    padding: 0 20px !important;
    z-index: 1000 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    height: auto !important;
    min-height: auto !important;
    display: block !important;
    flex-direction: unset !important;
    justify-content: unset !important;
    align-items: unset !important;
    margin: 0 !important;
    right: auto !important;
}

/* Override form styles */
body .chat-input-form,
.app-container .chat-input-form,
.chat-container .chat-input-form {
    width: 100% !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
    display: block !important;
    flex-direction: unset !important;
    align-items: unset !important;
    justify-content: unset !important;
    border-radius: 0 !important;
    overflow: visible !important;
}

/* Mobile overrides for ChatGPT design */
@media (max-width: 768px) {
    body .chat-input-container,
    .app-container .chat-input-container,
    .chat-container .chat-input-container,
    .sidebar.collapsed ~ .chat-container .chat-input-container,
    .sidebar:not(.collapsed) ~ .chat-container .chat-input-container {
        bottom: 10px !important;
        padding: 0 16px !important;
        max-width: 100% !important;
        width: 100% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
}

@media (max-width: 480px) {
    body .chat-input-container,
    .app-container .chat-input-container,
    .chat-container .chat-input-container,
    .sidebar.collapsed ~ .chat-container .chat-input-container,
    .sidebar:not(.collapsed) ~ .chat-container .chat-input-container {
        bottom: 8px !important;
        padding: 0 12px !important;
    }
}

.input-horizontal-row .input-main-area textarea::placeholder {
    color: #999 !important;
    font-size: 1rem !important;
}

.input-horizontal-row .right-actions {
    display: flex !important;
    align-items: center !important;
    flex-shrink: 0 !important;
    order: 3 !important;
    gap: 8px !important;
}

/* Arrow controls styling */
.arrow-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 2px !important;
}

.arrow-btn {
    width: 16px !important;
    height: 16px !important;
    min-width: 16px !important;
    min-height: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 2px !important;
    background: none !important;
    border: none !important;
    color: #999 !important;
    cursor: pointer !important;
    transition: background 0.2s !important;
}

.arrow-btn:hover {
    background: #f0f0f0 !important;
    color: #666 !important;
}

/* Update action buttons to match the image */
.input-horizontal-row .action-btn {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    min-height: 18px !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 3px !important;
    background: none !important;
    border: none !important;
    color: #666 !important;
    cursor: pointer !important;
    transition: background 0.2s !important;
}

.input-horizontal-row .action-btn:hover {
    background: #f5f5f5 !important;
}

/* Update send button to match the gray square in the image */
.input-horizontal-row .send-btn {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
    min-height: 32px !important;
    margin: 0 !important;
    background: #e0e0e0 !important;
    color: #666 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 6px !important;
    border: none !important;
    cursor: pointer !important;
    transition: background 0.2s !important;
}

.input-horizontal-row .send-btn:hover {
    background: #d0d0d0 !important;
}

.input-horizontal-row .send-btn:disabled {
    background: #f0f0f0 !important;
    cursor: not-allowed !important;
}

.input-wrapper {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
}

/* --- ChatGPT Classic Input Box Customization --- */
.input-row-chatgpt {
  display: flex;
  align-items: center;
  background: #f7f7f8;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 0 12px;
  min-height: 48px;
  width: 100%;
  gap: 0;
}
.left-actions-chatgpt {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 8px;
}
.action-btn-chatgpt {
  background: none;
  border: none;
  color: #6b6b6b;
  font-size: 22px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background 0.2s;
  cursor: pointer;
  padding: 0;
}
.action-btn-chatgpt:hover {
  background: #ececec;
  color: #222;
}
.icon-button-chatgpt {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 22px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  padding: 0;
}
.input-main-area-chatgpt {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}
.input-main-area-chatgpt textarea {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: 1.08rem;
  padding: 14px 0;
  min-height: 24px;
  max-height: 120px;
  color: #222;
  font-family: inherit;
  line-height: 1.5;
}
.input-main-area-chatgpt textarea::placeholder {
  color: #b0b0b0;
  font-size: 1.08rem;
}
.right-actions-chatgpt {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.send-btn-chatgpt {
  background: #ececec;
  border: none;
  color: #b0b0b0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
  margin-left: 4px;
  position: relative;
  cursor: pointer;
  padding: 0;
}
.send-btn-chatgpt:enabled {
  background: #19c37d;
  color: #fff;
}
.send-btn-chatgpt:enabled .send-icon {
  filter: brightness(0) invert(1);
}
.send-btn-chatgpt:disabled {
  background: #ececec;
  color: #b0b0b0;
  cursor: not-allowed;
}
.send-btn-chatgpt .send-icon {
  width: 20px;
  height: 20px;
  display: block;
  position: static;
  margin: 0;
  padding: 0;
  line-height: 0;
}
.send-btn-chatgpt .loading-icon {
  width: 20px;
  height: 20px;
  display: none;
}

@media (max-width: 700px) {
  .input-row-chatgpt {
    padding: 0 4px;
    min-height: 40px;
  }
  .action-btn-chatgpt, .icon-button-chatgpt {
    font-size: 18px;
    width: 28px;
    height: 28px;
  }
  .send-btn-chatgpt {
    width: 32px;
    height: 32px;
  }
  .input-main-area-chatgpt textarea {
    font-size: 0.98rem;
    padding: 10px 0;
  }
}

/* --- Pixel-Perfect ChatGPT Classic Input Bar --- */
.chatgpt-input-row {
  position: fixed;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 100vw;
  max-width: 700px;
  background: #f7f7f8;
  border: 1px solid #ececf1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  padding: 0 16px;
  z-index: 1002;
  margin-bottom: 0;
  min-height: 56px;
}
.chatgpt-left {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-right: 8px;
}
.chatgpt-action {
  background: none;
  border: none;
  color: #8e8ea0;
  font-size: 22px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
  cursor: pointer;
  padding: 0;
}
.chatgpt-action:hover, .chatgpt-escalate-icon:hover {
  background: #ececf1;
  color: #353740;
}
.chatgpt-escalate-icon {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 22px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0;
  transition: background 0.2s, color 0.2s;
}
.chatgpt-main {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}
.chatgpt-main textarea {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: 1.05rem;
  padding: 12px 0;
  min-height: 24px;
  max-height: 120px;
  color: #353740;
  font-family: inherit;
  line-height: 1.5;
}
.chatgpt-main textarea::placeholder {
  color: #8e8ea0;
  font-size: 1.05rem;
}
.chatgpt-right {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.chatgpt-send-btn {
  background: #ececf1;
  border: none;
  color: #b0b0b0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  margin-left: 4px;
  position: relative;
  cursor: pointer;
  padding: 0;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.chatgpt-send-btn:enabled {
  background: #19c37d;
  color: #fff;
  box-shadow: 0 2px 8px rgba(25,195,125,0.12);
}
.chatgpt-send-btn:enabled .send-icon {
  filter: brightness(0) invert(1);
}
.chatgpt-send-btn:disabled {
  background: #ececf1;
  color: #b0b0b0;
  cursor: not-allowed;
}
.chatgpt-send-btn .send-icon {
  width: 22px;
  height: 22px;
  display: block;
  position: static;
  margin: 0;
  padding: 0;
  line-height: 0;
}
.chatgpt-send-btn .loading-icon {
  width: 22px;
  height: 22px;
  display: none;
}
@media (max-width: 800px) {
  .chatgpt-input-row {
    max-width: 98vw;
    padding: 0 4vw;
  }
}
@media (max-width: 600px) {
  .chatgpt-input-row {
    max-width: 100vw;
    padding: 0 2vw;
    min-height: 44px;
  }
  .chatgpt-action, .chatgpt-escalate-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
  }
  .chatgpt-send-btn {
    width: 32px;
    height: 32px;
  }
  .chatgpt-main textarea {
    font-size: 0.98rem;
    padding: 8px 0;
  }
}

/* --- Exact ChatGPT Real Input Bar --- */
.chatgpt-real-input-row {
  position: fixed;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 100vw;
  max-width: 900px;
  background: #fff;
  border: 1px solid #d9d9e3;
  border-radius: 24px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  z-index: 1002;
  margin-bottom: 0;
  min-height: 56px;
  box-shadow: none;
}
.chatgpt-real-left, .chatgpt-real-right {
  display: flex;
  align-items: center;
  gap: 12px;
}
.chatgpt-real-action {
  background: none;
  border: none;
  color: #6e6e80;
  font-size: 22px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
  cursor: pointer;
  padding: 0;
}
.chatgpt-real-action:hover, .chatgpt-real-escalate:hover {
  background: #f4f4f8;
}
.chatgpt-real-escalate {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 22px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
  cursor: pointer;
  padding: 0;
}
.chatgpt-real-main {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}
.chatgpt-real-main textarea {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: 1.08rem;
  padding: 20px 0;
  min-height: 24px;
  max-height: 120px;
  color: #353740;
  font-family: inherit;
  line-height: 1.5;
}
.chatgpt-real-main textarea::placeholder {
  color: #b4b4c2;
  font-size: 1.08rem;
}
.chatgpt-real-send-btn {
  background: #f4f4f8;
  border: none;
  color: #b4b4c2;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
  margin-left: 4px;
  position: relative;
  cursor: pointer;
  padding: 0;
}
.chatgpt-real-send-btn:enabled {
  background: #19c37d;
  color: #fff;
}
.chatgpt-real-send-btn:enabled .send-icon {
  filter: brightness(0) invert(1);
}
.chatgpt-real-send-btn:disabled {
  background: #f4f4f8;
  color: #b4b4c2;
  cursor: not-allowed;
}
.chatgpt-real-send-btn .send-icon {
  width: 22px;
  height: 22px;
  display: block;
  position: static;
  margin: 0;
  padding: 0;
  line-height: 0;
}
.chatgpt-real-send-btn .loading-icon {
  width: 22px;
  height: 22px;
  display: none;
}
@media (max-width: 600px) {
  .chatgpt-real-input-row {
    padding: 0 8px;
    min-height: 44px;
  }
  .chatgpt-real-action, .chatgpt-real-escalate {
    font-size: 18px;
    width: 28px;
    height: 28px;
  }
  .chatgpt-real-send-btn {
    width: 32px;
    height: 32px;
  }
  .chatgpt-real-main textarea {
    font-size: 0.98rem;
    padding: 12px 0;
  }
}

/* ===== ROTATING GLOW ANIMATION FOR MESSAGE INPUT BOX ===== */
/* Clean implementation that only shows when text is present */

/* Ensure input wrapper has proper positioning for gradient animation */
.chatgpt-input-wrapper {
    position: relative !important;
    overflow: visible !important;
    border: 1px solid #d1d5db !important;
    border-radius: 26px !important;
    transition: all 0.3s ease !important;
    z-index: 1 !important;
}

/* CORRECT APPROACH - GRADIENT BORDER ONLY (NOT BACKGROUND) */
/* For PRE-LOGIN UI */
html body .pre-login-bottom-input-container .chatgpt-input-wrapper.has-text,
html body .pre-login-container .chatgpt-input-wrapper.has-text,
html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .pre-login-chat-input-form .chatgpt-input-wrapper.has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}

/* For LOGGED-IN UI - EXACT selectors from HTML */
footer.chat-input-container .chatgpt-input-wrapper.has-text,
.chat-input-container .chatgpt-input-wrapper.has-text,
.chat-input-form .chatgpt-input-wrapper.has-text,
html.logged-in .chat-input-container .chatgpt-input-wrapper.has-text,
body.logged-in .chat-input-container .chatgpt-input-wrapper.has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}

@keyframes rotateBorderGradient {
    0% {
        background: linear-gradient(#ffffff, #ffffff) padding-box,
                    conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
    }
    100% {
        background: linear-gradient(#ffffff, #ffffff) padding-box,
                    conic-gradient(from 360deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box;
    }
}

/* Remove the extra glow effect - gradient border is enough */

/* Keyframe animation for rotating the gradient */
@keyframes rotateGlow {
    0% {
        transform: rotate(0deg) !important;
    }
    100% {
        transform: rotate(360deg) !important;
    }
}

/* Mobile responsive adjustments for rotating glow animation - UNIVERSAL */
@media (max-width: 768px) {
    html body .chatgpt-input-wrapper.has-text::before,
    html body .pre-login-bottom-input-container .chatgpt-input-wrapper.has-text::before,
    html body .pre-login-container .chatgpt-input-wrapper.has-text::before,
    html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .pre-login-chat-input-form .chatgpt-input-wrapper.has-text::before,
    .chatgpt-input-wrapper.has-text::before {
        border-radius: 29px !important;
    }
}

@media (max-width: 480px) {
    html body .chatgpt-input-wrapper.has-text::before,
    html body .pre-login-bottom-input-container .chatgpt-input-wrapper.has-text::before,
    html body .pre-login-container .chatgpt-input-wrapper.has-text::before,
    html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .pre-login-chat-input-form .chatgpt-input-wrapper.has-text::before,
    .chatgpt-input-wrapper.has-text::before {
        border-radius: 27px !important;
    }
}

/* FALLBACK - Support for old input wrapper structure in logged-in UI */
html body .input-wrapper.has-text,
html body .chat-input-container .input-wrapper.has-text,
.input-wrapper.has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}

/* EMERGENCY FIX - Apply to ANY element with has-text class in logged-in UI */
html.logged-in .has-text,
body.logged-in .has-text {
    background: linear-gradient(#ffffff, #ffffff) padding-box,
                conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #5f27cd, #ff6b6b) border-box !important;
    border: 2px solid transparent !important;
    animation: rotateBorderGradient 3s linear infinite !important;
}
